<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

// Require user login
requireLogin();

$user_id = getCurrentUserId();
$user = getUserById($pdo, $user_id);

// Check if user has premium access
if ($user['package_type'] !== 'premium') {
    redirect('upgrade.php', 'Premium subscription required for auto-posting features.', 'warning');
}

$action = $_GET['action'] ?? 'dashboard';
$errors = [];
$success = false;

// Handle social media connection (mock)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $action === 'connect') {
    if (validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $platform = $_POST['platform'] ?? '';
        
        if (in_array($platform, SOCIAL_PLATFORMS)) {
            // Mock connection - in real app, this would handle OAuth
            setFlashMessage("Successfully connected to " . ucfirst($platform) . "! (Demo Mode)", 'success');
        } else {
            setFlashMessage('Invalid social platform.', 'error');
        }
    }
    redirect('social_posting.php');
}

// Handle post scheduling
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $action === 'schedule') {
    if (validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $festival_ids = $_POST['festivals'] ?? [];
        $platforms = $_POST['platforms'] ?? [];
        $auto_schedule = isset($_POST['auto_schedule']);
        
        if (empty($festival_ids)) {
            $errors[] = 'Please select at least one festival.';
        }
        if (empty($platforms)) {
            $errors[] = 'Please select at least one social platform.';
        }
        
        if (empty($errors)) {
            $scheduled_count = 0;
            
            foreach ($festival_ids as $festival_id) {
                // Get festival date
                $stmt = $pdo->prepare("SELECT date FROM festivals WHERE id = ?");
                $stmt->execute([$festival_id]);
                $festival = $stmt->fetch();
                
                if ($festival) {
                    $post_date = $auto_schedule ? $festival['date'] : date('Y-m-d');
                    
                    foreach ($platforms as $platform) {
                        // Check if already scheduled
                        $stmt = $pdo->prepare("SELECT id FROM scheduled_posts WHERE user_id = ? AND festival_id = ? AND social_platform = ?");
                        $stmt->execute([$user_id, $festival_id, $platform]);
                        
                        if (!$stmt->fetch()) {
                            $stmt = $pdo->prepare("INSERT INTO scheduled_posts (user_id, festival_id, post_date, social_platform, status) VALUES (?, ?, ?, ?, 'scheduled')");
                            if ($stmt->execute([$user_id, $festival_id, $post_date, $platform])) {
                                $scheduled_count++;
                            }
                        }
                    }
                }
            }
            
            if ($scheduled_count > 0) {
                setFlashMessage("Successfully scheduled $scheduled_count posts!", 'success');
            } else {
                setFlashMessage('No new posts were scheduled.', 'info');
            }
        }
    }
    redirect('social_posting.php');
}

// Handle post deletion
if ($action === 'delete' && isset($_GET['id'])) {
    if (validateCSRFToken($_GET['token'] ?? '')) {
        $post_id = intval($_GET['id']);
        $stmt = $pdo->prepare("DELETE FROM scheduled_posts WHERE id = ? AND user_id = ?");
        if ($stmt->execute([$post_id, $user_id])) {
            setFlashMessage('Scheduled post deleted successfully!', 'success');
        } else {
            setFlashMessage('Failed to delete scheduled post.', 'error');
        }
    }
    redirect('social_posting.php');
}

// Get user's scheduled posts
$stmt = $pdo->prepare("
    SELECT sp.*, f.name as festival_name, f.date as festival_date
    FROM scheduled_posts sp
    JOIN festivals f ON sp.festival_id = f.id
    WHERE sp.user_id = ?
    ORDER BY sp.post_date ASC, f.date ASC
");
$stmt->execute([$user_id]);
$scheduled_posts = $stmt->fetchAll();

// Get available festivals
$festivals = getAllFestivals($pdo);

// Mock connected platforms (in real app, this would come from database)
$connected_platforms = ['facebook', 'twitter']; // Demo: assume user connected these
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto Social Posting - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="<?php echo getAssetUrl('css/style.css'); ?>" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">🎉 <?php echo APP_NAME; ?></a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="free_generator.php">Free Generator</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="kit_generator.php">Kit Generator</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="social_posting.php">Auto Posting</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="logout.php">Logout</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">
                    <i class="fas fa-share-alt me-2"></i>Auto Social Posting
                    <span class="badge bg-warning text-dark ms-2">Premium</span>
                </h2>
                <p class="lead mb-4">
                    Automatically post your festival images to social media platforms on the right dates.
                </p>
            </div>
        </div>

        <?php displayFlashMessage(); ?>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Social Media Connections -->
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-link me-2"></i>Social Media Accounts
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php foreach (SOCIAL_PLATFORMS as $platform): ?>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fab fa-<?php echo $platform; ?> fa-2x me-3 text-<?php echo $platform === 'facebook' ? 'primary' : ($platform === 'twitter' ? 'info' : 'danger'); ?>"></i>
                                    <div>
                                        <h6 class="mb-0"><?php echo ucfirst($platform); ?></h6>
                                        <?php if (in_array($platform, $connected_platforms)): ?>
                                            <small class="text-success">Connected</small>
                                        <?php else: ?>
                                            <small class="text-muted">Not connected</small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div>
                                    <?php if (in_array($platform, $connected_platforms)): ?>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i>
                                        </span>
                                    <?php else: ?>
                                        <form method="POST" action="social_posting.php?action=connect" class="d-inline">
                                            <?php echo getCSRFTokenField(); ?>
                                            <input type="hidden" name="platform" value="<?php echo $platform; ?>">
                                            <button type="submit" class="btn btn-sm btn-outline-primary">
                                                Connect
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        
                        <div class="alert alert-info mt-3">
                            <small>
                                <i class="fas fa-info-circle me-1"></i>
                                Demo Mode: Social media connections are simulated for demonstration purposes.
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>Posting Stats
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Scheduled Posts:</span>
                            <strong><?php echo count($scheduled_posts); ?></strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Connected Platforms:</span>
                            <strong><?php echo count($connected_platforms); ?></strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Posts This Month:</span>
                            <strong>
                                <?php 
                                $posts_this_month = 0;
                                foreach ($scheduled_posts as $post) {
                                    if (date('Y-m', strtotime($post['post_date'])) === date('Y-m')) {
                                        $posts_this_month++;
                                    }
                                }
                                echo $posts_this_month;
                                ?>
                            </strong>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Schedule New Posts -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-plus me-2"></i>Schedule New Posts
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="social_posting.php?action=schedule">
                            <?php echo getCSRFTokenField(); ?>
                            
                            <div class="mb-3">
                                <label class="form-label">Select Festivals</label>
                                <div class="row">
                                    <?php foreach ($festivals as $festival): ?>
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       id="festival_<?php echo $festival['id']; ?>" 
                                                       name="festivals[]" value="<?php echo $festival['id']; ?>">
                                                <label class="form-check-label" for="festival_<?php echo $festival['id']; ?>">
                                                    <?php echo htmlspecialchars($festival['name']); ?>
                                                    <small class="text-muted">(<?php echo date('M j', strtotime($festival['date'])); ?>)</small>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Select Platforms</label>
                                <div class="row">
                                    <?php foreach ($connected_platforms as $platform): ?>
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       id="platform_<?php echo $platform; ?>" 
                                                       name="platforms[]" value="<?php echo $platform; ?>">
                                                <label class="form-check-label" for="platform_<?php echo $platform; ?>">
                                                    <i class="fab fa-<?php echo $platform; ?> me-2"></i>
                                                    <?php echo ucfirst($platform); ?>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="auto_schedule" name="auto_schedule" checked>
                                    <label class="form-check-label" for="auto_schedule">
                                        Auto-schedule posts on festival dates
                                    </label>
                                    <div class="form-text">
                                        If unchecked, posts will be scheduled for today
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-calendar-plus me-2"></i>Schedule Posts
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Scheduled Posts -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>Scheduled Posts
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($scheduled_posts)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">No scheduled posts</h6>
                                <p class="text-muted">Schedule your first festival posts above.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Festival</th>
                                            <th>Platform</th>
                                            <th>Scheduled Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($scheduled_posts as $post): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($post['festival_name']); ?></strong>
                                                    <br><small class="text-muted">
                                                        Festival Date: <?php echo date('M j, Y', strtotime($post['festival_date'])); ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <i class="fab fa-<?php echo $post['social_platform']; ?> me-2"></i>
                                                    <?php echo ucfirst($post['social_platform']); ?>
                                                </td>
                                                <td>
                                                    <?php echo date('M j, Y', strtotime($post['post_date'])); ?>
                                                    <?php if (strtotime($post['post_date']) < time()): ?>
                                                        <br><small class="text-muted">(Past due)</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status_class = 'secondary';
                                                    switch ($post['status']) {
                                                        case 'scheduled':
                                                            $status_class = 'primary';
                                                            break;
                                                        case 'posted':
                                                            $status_class = 'success';
                                                            break;
                                                        case 'failed':
                                                            $status_class = 'danger';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge bg-<?php echo $status_class; ?>">
                                                        <?php echo ucfirst($post['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($post['status'] === 'scheduled'): ?>
                                                        <a href="social_posting.php?action=delete&id=<?php echo $post['id']; ?>&token=<?php echo generateCSRFToken(); ?>" 
                                                           class="btn btn-sm btn-outline-danger delete-btn">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>
