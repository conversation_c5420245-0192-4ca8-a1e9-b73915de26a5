<?php
session_start();
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

// Create a test session if not logged in
if (!isLoggedIn()) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'Test User';
    $_SESSION['user_email'] = '<EMAIL>';
}

echo "<h1>Generate Image Endpoint Test</h1>";

// Test 1: Check if endpoint is accessible
echo "<h2>1. Basic Endpoint Test</h2>";
$test_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/generate_image.php';
echo "Testing URL: $test_url<br>";

// Test 2: Check required data
echo "<h2>2. Required Data Check</h2>";

// Get first festival and template
try {
    $festivals = getAllFestivals($pdo);
    if (empty($festivals)) {
        echo "❌ No festivals found<br>";
        exit;
    }
    
    $festival = $festivals[0];
    echo "✅ Using festival: {$festival['name']} (ID: {$festival['id']})<br>";
    
    $templates = getTemplatesByFestival($pdo, $festival['id']);
    if (empty($templates)) {
        echo "❌ No templates found for festival<br>";
        exit;
    }
    
    $template = $templates[0];
    echo "✅ Using template: {$template['name']} (ID: {$template['id']})<br>";
    
    // Check template file exists
    $template_path = ROOT_PATH . '/' . $template['image_path'];
    if (file_exists($template_path)) {
        echo "✅ Template file exists: {$template['image_path']}<br>";
    } else {
        echo "❌ Template file missing: {$template['image_path']}<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
    exit;
}

// Test 3: Simulate POST request
echo "<h2>3. POST Request Simulation</h2>";

$csrf_token = generateCSRFToken();
echo "CSRF Token: " . substr($csrf_token, 0, 10) . "...<br>";

$post_data = [
    'csrf_token' => $csrf_token,
    'festival_id' => $festival['id'],
    'template_id' => $template['id'],
    'message' => 'Test Message from Endpoint Test'
];

echo "POST Data:<br>";
echo "<pre>" . print_r($post_data, true) . "</pre>";

// Test 4: Check what happens when we call generate_image.php directly
echo "<h2>4. Direct Call Test</h2>";
echo "<p>Click the button below to test the endpoint:</p>";

?>

<form id="test-form" method="POST" action="generate_image.php">
    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
    <input type="hidden" name="festival_id" value="<?php echo $festival['id']; ?>">
    <input type="hidden" name="template_id" value="<?php echo $template['id']; ?>">
    <input type="hidden" name="message" value="Test Message from Direct Form">
    <button type="submit" class="btn btn-primary">Test Direct POST</button>
</form>

<div id="ajax-test-result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc; display: none;">
    <h4>AJAX Test Result:</h4>
    <pre id="ajax-response"></pre>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    // Add AJAX test button
    $('<button type="button" class="btn btn-success ms-2" id="ajax-test">Test AJAX Call</button>').insertAfter('#test-form button');
    
    $('#ajax-test').click(function() {
        $('#ajax-test-result').show();
        $('#ajax-response').text('Loading...');
        
        $.ajax({
            url: 'generate_image.php',
            type: 'POST',
            data: {
                csrf_token: '<?php echo $csrf_token; ?>',
                festival_id: <?php echo $festival['id']; ?>,
                template_id: <?php echo $template['id']; ?>,
                message: 'Test Message from AJAX'
            },
            dataType: 'json',
            success: function(response) {
                $('#ajax-response').text('SUCCESS:\n' + JSON.stringify(response, null, 2));
            },
            error: function(xhr, status, error) {
                $('#ajax-response').text(
                    'ERROR:\n' +
                    'Status: ' + xhr.status + '\n' +
                    'Status Text: ' + xhr.statusText + '\n' +
                    'Error: ' + error + '\n' +
                    'Response Text:\n' + xhr.responseText
                );
            }
        });
    });
});
</script>

<style>
.btn {
    padding: 8px 16px;
    margin: 4px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}
.btn-primary { background: #007bff; color: white; }
.btn-success { background: #28a745; color: white; }
.ms-2 { margin-left: 8px; }
</style>

<p><a href="free_generator.php">→ Go to Free Generator</a></p>
<p><a href="debug_free_generator.php">→ Run Debug Script</a></p>
<p><a href="index.php">← Back to Homepage</a></p>
