<?php
require_once 'config/config.php';
require_once 'config/session.php';

// Check if user is logged in
if (isLoggedIn()) {
    // Clear remember me cookie if it exists
    if (isset($_COOKIE['remember_token'])) {
        setcookie('remember_token', '', time() - 3600, '/');
    }
    
    // Logout user
    logoutUser();
    
    // Set success message
    setFlashMessage('You have been successfully logged out.', 'success');
}

// Redirect to homepage
redirect('index.php');
?>
