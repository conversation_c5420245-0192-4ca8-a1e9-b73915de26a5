<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

// Set JSON response header
header('Content-Type: application/json');

// Require user login
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'Please log in to generate kits.']);
    exit;
}

$user_id = getCurrentUserId();

// Validate user ID
if (!$user_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid user session. Please log in again.']);
    exit;
}

// Verify user exists in database
try {
    $stmt = $pdo->prepare("SELECT id, name, email, package_type FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    if (!$user) {
        // Log the issue for debugging
        error_log("User not found in database. User ID: " . $user_id . ", Session: " . print_r($_SESSION, true));
        echo json_encode(['success' => false, 'message' => 'User not found. Please log in again.']);
        exit;
    }
} catch (PDOException $e) {
    error_log("Database error in generate_kit.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error. Please try again.']);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
    exit;
}

// Validate CSRF token
if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
    echo json_encode(['success' => false, 'message' => 'Invalid request token.']);
    exit;
}

try {
    // Get and validate input
    $tagline = sanitize_input($_POST['tagline'] ?? '');
    $contact_info = sanitize_input($_POST['contact_info'] ?? '');
    $agree_terms = isset($_POST['agree_terms']);
    
    // Validate required fields
    if (empty($tagline)) {
        throw new Exception('Business tagline is required.');
    }
    
    if (empty($contact_info)) {
        throw new Exception('Contact information is required.');
    }
    
    if (!$agree_terms) {
        throw new Exception('You must agree to the terms and conditions.');
    }
    
    // Validate tagline length
    if (strlen($tagline) > 100) {
        throw new Exception('Tagline is too long. Maximum 100 characters allowed.');
    }
    
    // Handle logo upload
    $logo_path = null;
    $use_existing_logo = false;

    // Debug logging
    if (DEBUG_MODE) {
        error_log("FILES data: " . print_r($_FILES, true));
        error_log("POST data: " . print_r($_POST, true));
    }

    if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
        // New logo uploaded
        $upload_result = uploadFile($_FILES['logo'], ROOT_PATH . '/' . UPLOAD_DIR_LOGOS);
        if (!$upload_result['success']) {
            throw new Exception($upload_result['message']);
        }
        $logo_path = $upload_result['filepath'];
        if (DEBUG_MODE) {
            error_log("New logo uploaded: " . $logo_path);
        }

        // Save the new logo to database immediately
        $logo_path_for_db = str_replace(ROOT_PATH . '/', '', $logo_path);
        $stmt = $pdo->prepare("INSERT INTO user_uploads (user_id, logo_path, tagline, contact_info) VALUES (?, ?, ?, ?)
                              ON DUPLICATE KEY UPDATE logo_path = VALUES(logo_path), tagline = VALUES(tagline), contact_info = VALUES(contact_info)");
        $stmt->execute([$user_id, $logo_path_for_db, $tagline, $contact_info]);

        if (DEBUG_MODE) {
            error_log("Logo saved to database: " . $logo_path_for_db);
        }

    } else {
        // Check upload error
        if (isset($_FILES['logo'])) {
            $upload_error = $_FILES['logo']['error'];
            if (DEBUG_MODE) {
                error_log("Upload error code: " . $upload_error);
            }

            // Handle specific upload errors
            if ($upload_error === UPLOAD_ERR_NO_FILE) {
                // No file uploaded, check for existing logo
            } elseif ($upload_error === UPLOAD_ERR_INI_SIZE || $upload_error === UPLOAD_ERR_FORM_SIZE) {
                throw new Exception('Logo file is too large. Maximum size is 5MB.');
            } elseif ($upload_error === UPLOAD_ERR_PARTIAL) {
                throw new Exception('Logo upload was interrupted. Please try again.');
            } else {
                throw new Exception('Logo upload failed. Error code: ' . $upload_error);
            }
        }

        // Check if user has existing logo and wants to use it
        $stmt = $pdo->prepare("SELECT logo_path FROM user_uploads WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
        $stmt->execute([$user_id]);
        $existing_upload = $stmt->fetch();

        if (DEBUG_MODE) {
            error_log("Existing upload check: " . print_r($existing_upload, true));
        }

        if ($existing_upload && $existing_upload['logo_path']) {
            $logo_path = $existing_upload['logo_path'];
            // Handle both absolute and relative paths
            if (substr($logo_path, 0, 1) !== '/' && !file_exists($logo_path)) {
                $logo_path = ROOT_PATH . '/' . $logo_path;
            }

            // Verify the file actually exists
            if (file_exists($logo_path)) {
                $use_existing_logo = true;
                if (DEBUG_MODE) {
                    error_log("Using existing logo: " . $logo_path);
                }
            } else {
                if (DEBUG_MODE) {
                    error_log("Existing logo file not found: " . $logo_path);
                }
                throw new Exception('Please upload a business logo. Previous logo file not found.');
            }
        } else {
            throw new Exception('Please upload a business logo.');
        }
    }
    
    // Update user upload data if using existing logo (new logo already saved above)
    if ($use_existing_logo) {
        $stmt = $pdo->prepare("UPDATE user_uploads SET tagline = ?, contact_info = ? WHERE user_id = ?");
        $stmt->execute([$tagline, $contact_info, $user_id]);

        if (DEBUG_MODE) {
            error_log("Updated existing user upload data");
        }
    }
    
    // Get kit price
    $kit_price = getSetting($pdo, 'kit_price', DEFAULT_KIT_PRICE);
    
    // Create payment record
    $stmt = $pdo->prepare("INSERT INTO payments (user_id, amount, status, payment_method) VALUES (?, ?, 'pending', 'demo')");
    $stmt->execute([$user_id, $kit_price]);
    $payment_id = $pdo->lastInsertId();
    
    // For demo purposes, we'll simulate immediate payment success
    // In a real application, you would redirect to PayPal/Stripe here
    
    // Simulate payment processing delay
    sleep(1);
    
    // Update payment status to completed (demo)
    $stmt = $pdo->prepare("UPDATE payments SET status = 'completed', transaction_id = ? WHERE id = ?");
    $demo_transaction_id = 'DEMO_' . time() . '_' . $payment_id;
    $stmt->execute([$demo_transaction_id, $payment_id]);
    
    // Generate kit after "successful" payment
    $kit_result = generateFestivalKit($pdo, $user_id, $logo_path, $tagline, $contact_info);
    
    if (!$kit_result['success']) {
        throw new Exception($kit_result['message']);
    }
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Kit generated successfully!',
        'download_url' => 'download.php?file=' . urlencode(basename($kit_result['zip_path'])) . '&type=kit',
        'payment_id' => $payment_id
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Generate festival kit with all festival images
 */
function generateFestivalKit($pdo, $user_id, $logo_path, $tagline, $contact_info) {
    try {
        // Get all festivals
        $festivals = getAllFestivals($pdo);
        
        if (empty($festivals)) {
            return ['success' => false, 'message' => 'No festivals available.'];
        }
        
        $generated_files = [];
        $temp_dir = ROOT_PATH . '/' . UPLOAD_DIR_GENERATED . 'temp_' . $user_id . '_' . time() . '/';
        
        // Create temporary directory
        if (!mkdir($temp_dir, 0755, true)) {
            return ['success' => false, 'message' => 'Failed to create temporary directory.'];
        }
        
        // Generate image for each festival
        foreach ($festivals as $festival) {
            // Get templates for this festival
            $templates = getTemplatesByFestival($pdo, $festival['id']);
            
            if (empty($templates)) {
                continue; // Skip festivals without templates
            }
            
            // Use the first template for each festival
            $template = $templates[0];
            $template_path = 'uploads/'.$template['image_path'];

            if (!file_exists($template_path)) {
                continue; // Skip if template file doesn't exist
            }
            
            // Generate filename
            $safe_festival_name = preg_replace('/[^a-zA-Z0-9_-]/', '_', $festival['name']);
            $output_filename = $safe_festival_name . '_' . date('Y') . '.jpg';
            $output_path = $temp_dir . $output_filename;
            
            // Create custom message for this festival
            $festival_message = $tagline . "\n" . $contact_info;
            
            // Generate the image
            $generation_result = generateFestivalImage($template_path, $logo_path, $festival_message, $output_path);
            
            if ($generation_result['success']) {
                $generated_files[] = [
                    'path' => $output_path,
                    'name' => $output_filename
                ];
            }
        }
        
        if (empty($generated_files)) {
            // Clean up temp directory
            rmdir($temp_dir);
            return ['success' => false, 'message' => 'No images could be generated.'];
        }
        
        // Create ZIP file
        $zip_filename = 'festival_kit_' . $user_id . '_' . time() . '.zip';
        $zip_path = ROOT_PATH . '/' . UPLOAD_DIR_GENERATED . $zip_filename;
        
        if (!createZipFile($generated_files, $zip_path)) {
            // Clean up temp files
            foreach ($generated_files as $file) {
                if (file_exists($file['path'])) {
                    unlink($file['path']);
                }
            }
            rmdir($temp_dir);
            return ['success' => false, 'message' => 'Failed to create ZIP file.'];
        }
        
        // Clean up temporary files
        foreach ($generated_files as $file) {
            if (file_exists($file['path'])) {
                unlink($file['path']);
            }
        }
        rmdir($temp_dir);
        
        // Save kit download record
        $stmt = $pdo->prepare("INSERT INTO kit_downloads (user_id, generated_zip_path) VALUES (?, ?)");
        $stmt->execute([$user_id, UPLOAD_DIR_GENERATED . $zip_filename]);
        
        return [
            'success' => true,
            'zip_path' => $zip_path,
            'zip_filename' => $zip_filename,
            'file_count' => count($generated_files)
        ];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Kit generation failed: ' . $e->getMessage()];
    }
}
?>
