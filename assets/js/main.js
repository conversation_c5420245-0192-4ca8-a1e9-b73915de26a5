// FestivalKit Main JavaScript

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Template selection
    $('.template-option').click(function() {
        $('.template-option').removeClass('selected');
        $(this).addClass('selected');
        $('#selected_template').val($(this).data('template-id'));
    });

    // File upload drag and drop
    $('.file-upload-area').on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });

    $('.file-upload-area').on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });

    $('.file-upload-area').on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
        
        var files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            var fileInput = $(this).find('input[type="file"]')[0];
            fileInput.files = files;
            handleFileSelect(files[0], $(this));
        }
    });

    // File input change
    $('input[type="file"]').change(function() {
        if (this.files && this.files[0]) {
            handleFileSelect(this.files[0], $(this).closest('.file-upload-area'));
        }
    });

    // Handle file selection
    function handleFileSelect(file, uploadArea) {
        // Validate file type
        var allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedTypes.includes(file.type)) {
            showAlert('Please select a valid image file (JPG, JPEG, or PNG)', 'error');
            return;
        }

        // Validate file size (5MB max)
        if (file.size > 5242880) {
            showAlert('File size must be less than 5MB', 'error');
            return;
        }

        // Show preview
        var reader = new FileReader();
        reader.onload = function(e) {
            var preview = uploadArea.find('.file-preview');
            if (preview.length === 0) {
                uploadArea.append('<div class="file-preview mt-3"><img src="" class="image-preview" alt="Preview"><p class="mt-2 mb-0 text-success">File selected: ' + file.name + '</p></div>');
                preview = uploadArea.find('.file-preview');
            }
            preview.find('img').attr('src', e.target.result);
            preview.find('p').text('File selected: ' + file.name);
        };
        reader.readAsDataURL(file);
    }

    // Form validation
    $('form').submit(function(e) {
        var form = $(this);
        var isValid = true;

        // Check required fields
        form.find('[required]').each(function() {
            if (!$(this).val().trim()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });

        // Email validation
        form.find('input[type="email"]').each(function() {
            var email = $(this).val().trim();
            if (email && !isValidEmail(email)) {
                $(this).addClass('is-invalid');
                isValid = false;
            }
        });

        // Password confirmation
        var password = form.find('input[name="password"]').val();
        var confirmPassword = form.find('input[name="confirm_password"]').val();
        if (password && confirmPassword && password !== confirmPassword) {
            form.find('input[name="confirm_password"]').addClass('is-invalid');
            showAlert('Passwords do not match', 'error');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            showAlert('Please fill in all required fields correctly', 'error');
        }
    });

    // Email validation function
    function isValidEmail(email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Show alert function
    function showAlert(message, type) {
        var alertClass = 'alert-info';
        switch(type) {
            case 'success':
                alertClass = 'alert-success';
                break;
            case 'error':
                alertClass = 'alert-danger';
                break;
            case 'warning':
                alertClass = 'alert-warning';
                break;
        }

        var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                       message +
                       '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                       '</div>';

        // Remove existing alerts
        $('.alert').remove();
        
        // Add new alert at the top of the page
        $('body').prepend(alertHtml);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }

    // Image generator form
    $('#image-generator-form').submit(function(e) {
        e.preventDefault();
        
        var formData = new FormData(this);
        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.text();
        
        // Show loading state
        submitBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2"></span>Generating...');
        
        $.ajax({
            url: 'generate_image.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showAlert('Image generated successfully!', 'success');
                    if (response.download_url) {
                        // Show download button
                        $('#download-section').html('<a href="' + response.download_url + '" class="btn btn-success btn-lg" download><i class="fas fa-download me-2"></i>Download Image</a>').show();
                    }
                    if (response.preview_url) {
                        // Show preview
                        $('#preview-section').html('<img src="' + response.preview_url + '" class="image-preview" alt="Generated Image">').show();
                    }
                } else {
                    showAlert(response.message || 'Failed to generate image', 'error');
                }
            },
            error: function() {
                showAlert('An error occurred while generating the image', 'error');
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Kit generator form
    $('#kit-generator-form').submit(function(e) {
        e.preventDefault();
        
        var formData = new FormData(this);
        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.text();
        
        // Show loading state
        submitBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2"></span>Processing...');
        
        $.ajax({
            url: 'generate_kit.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    if (response.payment_required) {
                        // Redirect to payment
                        window.location.href = response.payment_url;
                    } else {
                        showAlert('Kit generated successfully!', 'success');
                        if (response.download_url) {
                            $('#download-section').html('<a href="' + response.download_url + '" class="btn btn-success btn-lg" download><i class="fas fa-download me-2"></i>Download Kit</a>').show();
                        }
                    }
                } else {
                    showAlert(response.message || 'Failed to generate kit', 'error');
                }
            },
            error: function() {
                showAlert('An error occurred while generating the kit', 'error');
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Social media connection
    $('.connect-social').click(function(e) {
        e.preventDefault();
        var platform = $(this).data('platform');
        
        // Mock social media connection
        showAlert('Social media connection is currently in demo mode', 'info');
        
        // In a real implementation, this would open OAuth popup
        // window.open('/auth/' + platform, 'social_auth', 'width=600,height=400');
    });

    // Admin data tables
    if ($('.data-table').length) {
        $('.data-table').each(function() {
            $(this).DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                language: {
                    search: "Search:",
                    lengthMenu: "Show _MENU_ entries",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    }
                }
            });
        });
    }

    // Confirm delete actions
    $('.delete-btn').click(function(e) {
        if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
            e.preventDefault();
        }
    });

    // Auto-hide flash messages
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });

    // Loading overlay
    function showLoading() {
        $('body').append('<div id="loading-overlay" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(0,0,0,0.5); z-index: 9999;"><div class="spinner-border text-light" style="width: 3rem; height: 3rem;"></div></div>');
    }

    function hideLoading() {
        $('#loading-overlay').remove();
    }

    // Export functionality
    $('.export-btn').click(function() {
        var exportType = $(this).data('export');
        var exportUrl = $(this).data('url');
        
        showLoading();
        
        window.location.href = exportUrl + '?format=' + exportType;
        
        setTimeout(hideLoading, 2000);
    });
});

// Global functions
function previewImage(input, previewId) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            $('#' + previewId).attr('src', e.target.result).show();
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showAlert('Copied to clipboard!', 'success');
    });
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    var k = 1024;
    var sizes = ['Bytes', 'KB', 'MB', 'GB'];
    var i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
