/* FestivalKit Custom Styles */

:root {
    --primary-color: #6f42c1;
    --secondary-color: #fd7e14;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 500px;
}

.hero-section .display-4 {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

/* Cards */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

/* Feature Icons */
.feature-icon i {
    margin-bottom: 1rem;
}

/* Forms */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #5a359a;
    border-color: #5a359a;
}

/* Dashboard */
.dashboard-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
}

.dashboard-card .card-body {
    padding: 2rem;
}

.dashboard-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

/* Image Generator */
.template-selector {
    max-height: 400px;
    overflow-y: auto;
}

.template-option {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 3px solid transparent;
}

.template-option:hover {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.template-option.selected {
    border-color: var(--success-color);
    box-shadow: 0 0 15px rgba(25, 135, 84, 0.3);
}

.template-option img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
}

/* File Upload */
.file-upload-area {
    border: 2px dashed #ccc;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(111, 66, 193, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--success-color);
    background-color: rgba(25, 135, 84, 0.05);
}

/* Progress Bar */
.progress {
    height: 25px;
    border-radius: 15px;
}

.progress-bar {
    border-radius: 15px;
    transition: width 0.6s ease;
}

/* Admin Panel */
.admin-sidebar {
    background-color: var(--dark-color);
    min-height: 100vh;
    padding-top: 2rem;
}

.admin-sidebar .nav-link {
    color: #adb5bd;
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    transition: all 0.3s ease;
}

.admin-sidebar .nav-link:hover,
.admin-sidebar .nav-link.active {
    color: white;
    background-color: var(--primary-color);
}

.admin-content {
    padding: 2rem;
}

/* Stats Cards */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stats-card h4 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-card p {
    margin-bottom: 0;
    opacity: 0.9;
}

/* Tables */
.table-responsive {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.table thead th {
    background-color: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: rgba(111, 66, 193, 0.05);
}

/* Buttons */
.btn {
    border-radius: 25px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-sm {
    padding: 0.25rem 1rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.125rem;
}

/* Alerts */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Footer */
footer {
    margin-top: auto;
}

/* Responsive */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 3rem 0;
    }
    
    .hero-section .display-4 {
        font-size: 2rem;
    }
    
    .dashboard-card h3 {
        font-size: 2rem;
    }
    
    .admin-sidebar {
        min-height: auto;
    }
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Image Preview */
.image-preview {
    max-width: 100%;
    max-height: 300px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Social Media Icons */
.social-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 0 5px;
    transition: all 0.3s ease;
}

.social-icon:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.social-icon.facebook { background-color: #3b5998; }
.social-icon.twitter { background-color: #1da1f2; }
.social-icon.instagram { background-color: #e4405f; }
.social-icon.linkedin { background-color: #0077b5; }

/* Package Cards */
.package-card {
    border: 2px solid #e9ecef;
    border-radius: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.package-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.package-card.featured {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(111, 66, 193, 0.05) 0%, rgba(253, 126, 20, 0.05) 100%);
}

.package-card.featured::before {
    content: "Most Popular";
    position: absolute;
    top: 15px;
    right: -30px;
    background: var(--primary-color);
    color: white;
    padding: 5px 40px;
    font-size: 0.8rem;
    font-weight: bold;
    transform: rotate(45deg);
}

.package-price {
    font-size: 3rem;
    font-weight: bold;
    color: var(--primary-color);
}

.package-price small {
    font-size: 1rem;
    color: #6c757d;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-50px); }
    to { opacity: 1; transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0,0,0,0.175);
}

.border-radius-lg {
    border-radius: 15px;
}

.border-radius-xl {
    border-radius: 25px;
}

/* Additional Professional Styling */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

/* Enhanced Cards */
.card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Enhanced Buttons */
.btn {
    font-weight: 500;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #5a359a 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(111, 66, 193, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a359a 0%, var(--primary-color) 100%);
    box-shadow: 0 6px 20px rgba(111, 66, 193, 0.4);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #146c43 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(25, 135, 84, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #146c43 0%, var(--success-color) 100%);
    box-shadow: 0 6px 20px rgba(25, 135, 84, 0.4);
    transform: translateY(-1px);
}

/* Enhanced Navigation */
.navbar {
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: rgba(255,255,255,0.8);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 80%;
}

/* Enhanced Forms */
.form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.9);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.15);
    background: white;
    transform: translateY(-1px);
}

.form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 16px;
    transition: all 0.3s ease;
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.15);
}

/* Enhanced File Upload */
.file-upload-area {
    border: 3px dashed #dee2e6;
    border-radius: 15px;
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background: linear-gradient(135deg, rgba(111, 66, 193, 0.02) 0%, rgba(253, 126, 20, 0.02) 100%);
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(111, 66, 193, 0.05) 0%, rgba(253, 126, 20, 0.05) 100%);
    transform: translateY(-2px);
}

.file-upload-area.dragover {
    border-color: var(--success-color);
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.05) 0%, rgba(25, 135, 84, 0.1) 100%);
    transform: scale(1.02);
}

/* Enhanced Tables */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0,0,0,0.05);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, #5a359a 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
    padding: 1rem;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(111, 66, 193, 0.03) 0%, rgba(253, 126, 20, 0.03) 100%);
    transform: scale(1.01);
}

/* Enhanced Badges */
.badge {
    font-weight: 500;
    letter-spacing: 0.5px;
    padding: 0.5em 0.8em;
    border-radius: 20px;
}

/* Enhanced Alerts */
.alert {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-left: 4px solid;
}

.alert-success {
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.1) 0%, rgba(25, 135, 84, 0.05) 100%);
    border-left-color: var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%);
    border-left-color: var(--danger-color);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    border-left-color: var(--warning-color);
}

.alert-info {
    background: linear-gradient(135deg, rgba(13, 202, 240, 0.1) 0%, rgba(13, 202, 240, 0.05) 100%);
    border-left-color: var(--info-color);
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Enhanced Modals */
.modal-content {
    border: none;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 1px solid rgba(0,0,0,0.05);
    border-radius: 15px 15px 0 0;
}

.modal-footer {
    border-top: 1px solid rgba(0,0,0,0.05);
    border-radius: 0 0 15px 15px;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color) 0%, #5a359a 100%);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a359a 0%, var(--primary-color) 100%);
}

/* Modern Dashboard Enhancements */
.dashboard-gradient {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Smooth Animations */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .welcome-hero {
        padding: 2rem 1rem;
    }

    .welcome-hero h1 {
        font-size: 2rem;
    }

    .stats-card-modern {
        margin-bottom: 1rem;
    }

    .action-card {
        padding: 1.5rem;
    }

    .modern-navbar {
        padding: 0.5rem 0;
    }
}

@media (max-width: 576px) {
    .stats-card-modern h3 {
        font-size: 2rem;
    }

    .action-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .btn-modern {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}

/* Print Styles */
@media print {
    .navbar, .btn, .alert, .modal { display: none !important; }
    .card { box-shadow: none !important; border: 1px solid #ddd !important; }
    body { font-size: 12pt; }
    .modern-card, .action-card, .stats-card-modern {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
