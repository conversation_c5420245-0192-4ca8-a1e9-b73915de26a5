<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

// Require user login
requireLogin();

$user_id = getCurrentUserId();

// Debug information
if (!$user_id) {
    setFlashMessage('error', 'No user ID in session. Please log in again.');
    redirect('quick_login.php');
}

$user = getUserById($pdo, $user_id);
if (!$user) {
    setFlashMessage('error', 'User not found in database (ID: ' . $user_id . '). Please log in again.');
    redirect('quick_login.php');
}

// Get kit price from settings
$kit_price = getSetting($pdo, 'kit_price', DEFAULT_KIT_PRICE);

// Get all festivals
$festivals = getAllFestivals($pdo);

// Get user's existing upload data
$stmt = $pdo->prepare("SELECT * FROM user_uploads WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
$stmt->execute([$user_id]);
$existing_upload = $stmt->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kit Generator - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            min-height: 100vh;
        }

        .generator-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            margin: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .modern-navbar {
            background: rgba(255, 255, 255, 0.15) !important;
            backdrop-filter: blur(20px);
            border: none !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
            padding: 15px 0 !important;
        }

        .navbar-brand {
            font-weight: 800 !important;
            font-size: 1.8rem !important;
            color: white !important;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500 !important;
            padding: 10px 20px !important;
            border-radius: 25px !important;
            transition: all 0.3s ease !important;
        }

        .nav-link:hover, .nav-link.active {
            background: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            transform: translateY(-2px);
        }

        .generator-header {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            margin-bottom: 30px;
        }

        .generator-header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
        }

        .modern-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            border: none;
            transition: all 0.3s ease;
        }

        .modern-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .form-control, .form-select {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #11998e;
            box-shadow: 0 0 0 0.2rem rgba(17, 153, 142, 0.25);
        }

        .btn-modern {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-weight: 700;
            color: white;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }

        .btn-modern:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(17, 153, 142, 0.4);
            color: white;
        }

        .price-badge {
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
            color: white;
            padding: 10px 25px;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.2rem;
            display: inline-block;
            margin-bottom: 20px;
        }

        .alert {
            border-radius: 15px;
            border: none;
            padding: 20px;
            margin-bottom: 30px;
        }

        .alert-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
        }

        .alert-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
            color: white;
        }

        .file-upload-area {
            border: 2px dashed #ddd;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
            position: relative;
            min-height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .file-upload-area:hover {
            border-color: #007bff;
            background: #e3f2fd;
            transform: translateY(-2px);
        }

        .file-upload-area.dragover {
            border-color: #28a745;
            background: #d4edda;
            transform: scale(1.02);
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
        }

        .file-upload-area.dragover::before {
            content: "Drop your logo here!";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(40, 167, 69, 0.95);
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            font-weight: bold;
            font-size: 1.1rem;
            z-index: 10;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <!-- Modern Navigation -->
    <nav class="navbar navbar-expand-lg modern-navbar">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-sparkles me-2"></i><?php echo APP_NAME; ?>
            </a>
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <i class="fas fa-bars text-white"></i>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="free_generator.php">
                            <i class="fas fa-magic me-2"></i>Free Generator
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="kit_generator.php">
                            <i class="fas fa-box me-2"></i>Kit Generator
                        </a>
                    </li>
                    <?php if (($user['package_type'] ?? '') === 'premium'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="social_posting.php">
                            <i class="fas fa-share-alt me-2"></i>Auto Posting
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="template_designer.php">
                            <i class="fas fa-paint-brush me-2"></i>Template Designer
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-2"></i><?php echo htmlspecialchars($user['name'] ?? 'User'); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="generator-container">
        <?php displayFlashMessage(); ?>

        <div class="generator-header">
            <h1><i class="fas fa-box me-3"></i>Festival Kit Generator</h1>
            <p class="mb-3">Generate professional festival images for all festivals with your business branding</p>
            <div class="price-badge">
                <i class="fas fa-tag me-2"></i>Only <?php echo formatCurrency($kit_price); ?>
            </div>
            <p class="mb-0">Perfect for social media marketing throughout the year!</p>
        </div>

        <div class="row">
            <!-- Kit Generator Form -->
            <div class="col-lg-8">
                <div class="modern-card">
                    <h3 class="mb-4"><i class="fas fa-star me-2"></i>Premium Kit Generator</h3>
                        <form id="kit-generator-form" enctype="multipart/form-data">
                            <?php echo getCSRFTokenField(); ?>
                            
                            <!-- Business Logo -->
                            <div class="mb-4">
                                <label class="form-label">
                                    <i class="fas fa-image me-2"></i>Business Logo <span class="text-danger">*</span>
                                </label>
                                <div class="file-upload-area" onclick="document.getElementById('logo_file').click();">
                                    <input type="file" id="logo_file" name="logo" accept="image/*" style="display: none;" required>
                                    <div class="text-center">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <p class="mb-2">Click to upload your business logo</p>
                                        <small class="text-muted">PNG, JPG up to 5MB (PNG with transparent background recommended)</small>
                                    </div>
                                </div>
                                <?php if ($existing_upload && $existing_upload['logo_path']): ?>
                                    <div class="mt-3">
                                        <small class="text-muted">Previously uploaded logo:</small>
                                        <div class="d-flex align-items-center mt-2">
                                            <img src="<?php echo getUploadUrl($existing_upload['logo_path']); ?>" 
                                                 alt="Previous Logo" style="height: 50px;" class="me-3 rounded">
                                            <button type="button" class="btn btn-sm btn-outline-secondary" id="use-previous-logo">
                                                Use Previous Logo
                                            </button>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Business Tagline -->
                            <div class="mb-4">
                                <label for="tagline" class="form-label">
                                    <i class="fas fa-tag me-2"></i>Business Tagline <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="tagline" name="tagline" 
                                       value="<?php echo htmlspecialchars($existing_upload['tagline'] ?? ''); ?>"
                                       placeholder="e.g., Your Trusted Partner in Success" 
                                       maxlength="100" required>
                                <div class="form-text">This will appear on all your festival images</div>
                            </div>

                            <!-- Contact Information -->
                            <div class="mb-4">
                                <label for="contact_info" class="form-label">
                                    <i class="fas fa-phone me-2"></i>Contact Information <span class="text-danger">*</span>
                                </label>
                                <textarea class="form-control" id="contact_info" name="contact_info" rows="3" 
                                          placeholder="Phone: ****** 567 8900&#10;Email: <EMAIL>&#10;Website: www.yourbusiness.com" 
                                          required><?php echo htmlspecialchars($existing_upload['contact_info'] ?? ''); ?></textarea>
                                <div class="form-text">Your contact details will be included in the images</div>
                            </div>

                            <!-- Festival Preview -->
                            <div class="mb-4">
                                <label class="form-label">
                                    <i class="fas fa-calendar-alt me-2"></i>Included Festivals (<?php echo count($festivals); ?> total)
                                </label>
                                <div class="row">
                                    <?php foreach (array_slice($festivals, 0, 6) as $festival): ?>
                                        <div class="col-md-4 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-check text-success me-2"></i>
                                                <small><?php echo htmlspecialchars($festival['name']); ?></small>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                    <?php if (count($festivals) > 6): ?>
                                        <div class="col-12">
                                            <small class="text-muted">...and <?php echo count($festivals) - 6; ?> more festivals</small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Terms and Conditions -->
                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="agree_terms" name="agree_terms" required>
                                    <label class="form-check-label" for="agree_terms">
                                        I agree to the <a href="#" target="_blank">Terms and Conditions</a> and 
                                        <a href="#" target="_blank">Privacy Policy</a>
                                    </label>
                                </div>
                            </div>

                            <!-- Generate Button -->
                            <div class="d-grid">
                                <button type="submit" class="btn btn-modern btn-lg">
                                    <i class="fas fa-magic me-2"></i>
                                    Generate Kit - <?php echo formatCurrency($kit_price); ?>
                                </button>
                            </div>

                            <div class="text-center mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-lock me-1"></i>
                                    Secure payment processing. You will be redirected to complete payment.
                                </small>
                            </div>
                        </form>
                    </div>
                </div>
            

            <!-- Kit Information -->
            <div class="col-lg-4">
                <!-- What's Included -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-gift me-2"></i>What's Included
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <?php echo count($festivals); ?> high-quality festival images
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Your logo on every image
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Custom tagline and contact info
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                1200x800px resolution (perfect for social media)
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                Instant ZIP download
                            </li>
                            <li class="mb-0">
                                <i class="fas fa-check text-success me-2"></i>
                                Commercial usage rights
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Payment Methods -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                            <i class="fas fa-credit-card me-2"></i>Payment Methods
                        </h6>
                    </div>
                    <div class="card-body text-center">
                        <div class="row">
                            <div class="col-6">
                                <i class="fab fa-paypal fa-2x text-primary mb-2"></i>
                                <p class="small mb-0">PayPal</p>
                            </div>
                            <div class="col-6">
                                <i class="fab fa-stripe fa-2x text-info mb-2"></i>
                                <p class="small mb-0">Stripe</p>
                            </div>
                        </div>
                        <small class="text-muted">Secure payment processing</small>
                    </div>
                </div>

                <!-- Sample Preview -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-eye me-2"></i>Sample Preview
                        </h6>
                    </div>
                    <div class="card-body text-center">
                        <img src="<?php echo getAssetUrl('images/sample-festival-image.jpg'); ?>" 
                             alt="Sample Festival Image" class="img-fluid rounded shadow-sm mb-3"
                             style="max-height: 200px;">
                        <p class="small text-muted mb-0">
                            Example of how your branded festival image will look
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Download Section (Hidden initially) -->
        <div class="row mt-4" id="download-section" style="display: none;">
            <div class="col-12">
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle me-2"></i>Kit Generated Successfully!</h5>
                    <p class="mb-3">Your festival kit has been generated and is ready for download.</p>
                    <!-- Download button will be inserted here -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        $(document).ready(function() {
            console.log('Kit Generator JavaScript loaded');

            // Debug: Check if elements exist
            var fileInput = document.getElementById('logo_file');
            var form = document.getElementById('kit-generator-form');
            console.log('File input found:', !!fileInput);
            console.log('Form found:', !!form);
            // Use previous logo button
            $(document).on('click', '#use-previous-logo', function() {
                // Remove required attribute and show that previous logo will be used
                var fileInput = document.getElementById('logo_file');
                if (fileInput) {
                    fileInput.removeAttribute('required');
                }
                $('.file-upload-area').html('<div class="text-center text-success"><i class="fas fa-check-circle fa-2x mb-2"></i><p>Using previously uploaded logo</p></div>');

                // Set a flag to indicate using previous logo
                $('#kit-generator-form').data('use-previous-logo', true);
            });

            // Character counter for tagline
            $('#tagline').on('input', function() {
                var length = $(this).val().length;
                var maxLength = 100;

                if (length > maxLength) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            // File upload area click handler
            $('.file-upload-area').click(function(e) {
                e.preventDefault();
                var fileInput = document.getElementById('logo_file');
                if (fileInput) {
                    fileInput.click();
                } else {
                    console.error('Logo file input not found');
                }
            });

            // Drag and drop functionality
            $('.file-upload-area').on('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('dragover');
            });

            $('.file-upload-area').on('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('dragover');
            });

            $('.file-upload-area').on('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('dragover');

                var files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    var file = files[0];

                    // Validate file type
                    if (!file.type.match('image.*')) {
                        alert('Please select an image file.');
                        return;
                    }

                    // Validate file size (5MB)
                    if (file.size > 5 * 1024 * 1024) {
                        alert('File size must be less than 5MB.');
                        return;
                    }

                    // Set the file to the input
                    var fileInput = document.getElementById('logo_file');
                    if (fileInput) {
                        var dt = new DataTransfer();
                        dt.items.add(file);
                        fileInput.files = dt.files;

                        // Trigger change event
                        $(fileInput).trigger('change');
                    }
                }
            });

            // File upload change handler
            $(document).on('change', '#logo_file', function() {
                var file = this.files[0];
                if (file) {
                    // Validate file type
                    if (!file.type.match('image.*')) {
                        alert('Please select an image file.');
                        this.value = '';
                        return;
                    }

                    // Validate file size (5MB)
                    if (file.size > 5 * 1024 * 1024) {
                        alert('File size must be less than 5MB.');
                        this.value = '';
                        return;
                    }

                    var reader = new FileReader();
                    reader.onload = function(e) {
                        $('.file-upload-area').html(
                            '<div class="text-center">' +
                            '<img src="' + e.target.result + '" style="max-width: 100px; max-height: 100px; border-radius: 5px;" class="mb-2">' +
                            '<p class="mb-0"><strong>' + file.name + '</strong></p>' +
                            '<small class="text-muted">' + (file.size / 1024 / 1024).toFixed(2) + ' MB</small>' +
                            '<br><small class="text-success">✓ Ready to upload</small>' +
                            '</div>'
                        );
                    };
                    reader.readAsDataURL(file);
                } else {
                    // Reset upload area if no file
                    $('.file-upload-area').html(
                        '<div class="text-center">' +
                        '<i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>' +
                        '<p class="mb-2">Click to upload your business logo</p>' +
                        '<small class="text-muted">PNG, JPG up to 5MB (PNG with transparent background recommended)</small>' +
                        '</div>'
                    );
                }
            });

            // Form submission
            $('#kit-generator-form').submit(function(e) {
                e.preventDefault();

                var $form = $(this);
                var $submitBtn = $form.find('button[type="submit"]');
                var originalText = $submitBtn.html();

                // Validate file upload
                var fileInput = document.getElementById('logo_file');
                if (!fileInput) {
                    console.error('Logo file input not found');
                    alert('Error: Logo upload field not found. Please refresh the page.');
                    return;
                }

                var usePreviousLogo = $form.data('use-previous-logo');
                if (!usePreviousLogo && (!fileInput.files || fileInput.files.length === 0)) {
                    alert('Please upload a business logo or click "Use Previous Logo" if you have one.');
                    return;
                }

                // Show loading state
                $submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Processing...');

                // Create FormData for file upload
                var formData = new FormData(this);

                // Debug: Log form data
                console.log('Form submission data:');
                for (var pair of formData.entries()) {
                    console.log(pair[0] + ': ' + pair[1]);
                }

                $.ajax({
                    url: 'generate_kit.php',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            // Show success message
                            showAlert('Kit generated successfully!', 'success');

                            // Show download section
                            var downloadBtn = '<a href="' + response.download_url + '" class="btn btn-success btn-lg" download>' +
                                            '<i class="fas fa-download me-2"></i>Download Festival Kit</a>';
                            $('#download-section .alert').append(downloadBtn);
                            $('#download-section').show();

                            // Scroll to download section
                            $('html, body').animate({
                                scrollTop: $('#download-section').offset().top
                            }, 1000);
                        } else {
                            showAlert(response.message || 'Failed to generate kit. Please try again.', 'error');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', error);
                        showAlert('An error occurred while generating the kit. Please try again.', 'error');
                    },
                    complete: function() {
                        // Reset button
                        $submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            });

            // Alert function
            function showAlert(message, type) {
                var alertClass = 'alert-info';
                switch(type) {
                    case 'success': alertClass = 'alert-success'; break;
                    case 'error': alertClass = 'alert-danger'; break;
                    case 'warning': alertClass = 'alert-warning'; break;
                }

                var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                               message +
                               '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                               '</div>';

                // Remove existing alerts
                $('.alert').not('#download-section .alert').remove();

                // Add new alert at the top
                $('.container').prepend(alertHtml);

                // Auto-dismiss after 5 seconds
                setTimeout(function() {
                    $('.alert').not('#download-section .alert').fadeOut();
                }, 5000);
            }
        });
    </script>
</body>
</html>
