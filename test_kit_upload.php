<?php
session_start();
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

// Create a test session if not logged in
if (!isLoggedIn()) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'Test User';
    $_SESSION['user_email'] = '<EMAIL>';
}

echo "<h1>🧪 Kit Generator Upload Test</h1>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>📤 Upload Test Results</h2>";
    
    echo "<h3>POST Data:</h3>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    echo "<h3>FILES Data:</h3>";
    echo "<pre>" . print_r($_FILES, true) . "</pre>";
    
    if (isset($_FILES['logo'])) {
        $file = $_FILES['logo'];
        echo "<h3>File Analysis:</h3>";
        echo "Name: " . $file['name'] . "<br>";
        echo "Type: " . $file['type'] . "<br>";
        echo "Size: " . $file['size'] . " bytes (" . round($file['size'] / 1024 / 1024, 2) . " MB)<br>";
        echo "Error: " . $file['error'] . "<br>";
        echo "Temp file: " . $file['tmp_name'] . "<br>";
        
        if ($file['error'] === UPLOAD_ERR_OK) {
            echo "<div style='color: green;'>✅ File upload successful!</div>";
            
            // Test the upload function
            $upload_result = uploadFile($file, ROOT_PATH . '/' . UPLOAD_DIR_LOGOS);
            echo "<h3>Upload Function Result:</h3>";
            echo "<pre>" . print_r($upload_result, true) . "</pre>";
            
            if ($upload_result['success']) {
                $uploaded_file = $upload_result['filepath'];
                echo "<div style='color: green;'>✅ File saved successfully!</div>";
                echo "File path: " . $uploaded_file . "<br>";
                
                if (file_exists($uploaded_file)) {
                    echo "✅ File exists on disk<br>";
                    $image_info = getimagesize($uploaded_file);
                    if ($image_info) {
                        echo "✅ Valid image: {$image_info[0]}x{$image_info[1]}<br>";
                        
                        // Show preview
                        $preview_url = getUploadUrl2('uploads/logos/' . basename($uploaded_file));
                        echo "<h3>Preview:</h3>";
                        echo "<img src='$preview_url' style='max-width: 200px; border: 1px solid #ccc;'><br>";
                    } else {
                        echo "❌ Not a valid image<br>";
                    }
                } else {
                    echo "❌ File not found on disk<br>";
                }
            } else {
                echo "<div style='color: red;'>❌ Upload function failed</div>";
            }
        } else {
            echo "<div style='color: red;'>❌ File upload error: " . $file['error'] . "</div>";
        }
    } else {
        echo "<div style='color: red;'>❌ No file uploaded</div>";
    }
    
    echo "<hr>";
}
?>

<h2>📋 Upload Test Form</h2>
<form method="POST" enctype="multipart/form-data" style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
    <div style="margin-bottom: 15px;">
        <label><strong>Business Name:</strong></label><br>
        <input type="text" name="business_name" value="Test Business" style="width: 300px; padding: 8px; border-radius: 5px; border: 1px solid #ddd;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label><strong>Tagline:</strong></label><br>
        <input type="text" name="tagline" value="Test Tagline" style="width: 300px; padding: 8px; border-radius: 5px; border: 1px solid #ddd;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label><strong>Contact Info:</strong></label><br>
        <input type="text" name="contact_info" value="<EMAIL> | 123-456-7890" style="width: 300px; padding: 8px; border-radius: 5px; border: 1px solid #ddd;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label><strong>Logo File:</strong></label><br>
        <input type="file" name="logo" accept="image/*" required style="padding: 8px;">
    </div>
    
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    
    <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        Test Upload
    </button>
</form>

<h2>🔗 Quick Links</h2>
<div style="margin: 20px 0;">
    <a href="kit_generator.php" style="display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">Kit Generator</a>
    <a href="test_complete_system.php" style="display: inline-block; padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">System Test</a>
    <a href="index.php" style="display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">Homepage</a>
</div>

<h2>📝 Instructions</h2>
<div style="background: #e3f2fd; padding: 15px; border-radius: 5px;">
    <p><strong>How to test:</strong></p>
    <ol>
        <li>Select an image file using the file input above</li>
        <li>Click "Test Upload" to see detailed upload process</li>
        <li>Check if the file is properly processed and saved</li>
        <li>If successful, try the actual kit generator</li>
    </ol>
    
    <p><strong>What to look for:</strong></p>
    <ul>
        <li>✅ File upload successful (error code 0)</li>
        <li>✅ Upload function returns success</li>
        <li>✅ File exists on disk</li>
        <li>✅ Valid image dimensions</li>
        <li>✅ Preview displays correctly</li>
    </ul>
</div>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
