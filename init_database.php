<?php
require_once 'config/config.php';
require_once 'config/database.php';

echo "<h1>Database Initialization</h1>";

try {
    // Create users table if not exists
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        package_type ENUM('free', 'premium') DEFAULT 'free',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Users table ready</p>";
    
    // Create festivals table if not exists
    $sql = "CREATE TABLE IF NOT EXISTS festivals (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
        date DATE NOT NULL,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Festivals table ready</p>";
    
    // Create templates table if not exists
    $sql = "CREATE TABLE IF NOT EXISTS templates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        festival_id INT,
        image_path VARCHAR(255) NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (festival_id) REFERENCES festivals(id) ON DELETE CASCADE
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Templates table ready</p>";
    
    // Create user_uploads table if not exists
    $sql = "CREATE TABLE IF NOT EXISTS user_uploads (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        logo_path VARCHAR(255),
        tagline VARCHAR(255),
        contact_info TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ User uploads table ready</p>";
    
    // Create payments table if not exists
    $sql = "CREATE TABLE IF NOT EXISTS payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        currency VARCHAR(3) DEFAULT 'USD',
        status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
        transaction_id VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Payments table ready</p>";
    
    // Insert sample festivals if none exist
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM festivals");
    if ($stmt->fetch()['count'] == 0) {
        $festivals = [
            ['New Year 2024', '2024-01-01', 'Celebrate the new year with style'],
            ['Valentine\'s Day', '2024-02-14', 'Show your love with beautiful designs'],
            ['Easter', '2024-03-31', 'Spring celebration with vibrant colors'],
            ['Mother\'s Day', '2024-05-12', 'Honor mothers with special designs'],
            ['Independence Day', '2024-07-04', 'Patriotic celebration designs'],
            ['Halloween', '2024-10-31', 'Spooky and fun Halloween themes'],
            ['Thanksgiving', '2024-11-28', 'Gratitude and harvest celebration'],
            ['Christmas', '2024-12-25', 'Festive holiday celebration'],
            ['Diwali', '2024-11-01', 'Festival of lights celebration'],
            ['Eid Mubarak', '2024-04-10', 'Islamic celebration of joy']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO festivals (name, date, description) VALUES (?, ?, ?)");
        foreach ($festivals as $festival) {
            $stmt->execute($festival);
        }
        echo "<p style='color: blue;'>📅 Sample festivals added</p>";
    }
    
    // Create test user if not exists
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    if ($stmt->fetch()['count'] == 0) {
        $password_hash = password_hash('test123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (name, email, password, package_type) VALUES (?, ?, ?, ?)");
        $stmt->execute(['Test User', '<EMAIL>', $password_hash, 'premium']);
        echo "<p style='color: blue;'>👤 Test user created (<EMAIL> / test123)</p>";
    }
    
    echo "<hr>";
    echo "<h3>Database Status:</h3>";
    
    $tables = ['users', 'festivals', 'templates', 'user_uploads', 'payments'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
        $count = $stmt->fetch()['count'];
        echo "<p>📊 $table: $count records</p>";
    }
    
    echo "<hr>";
    echo "<p><a href='quick_login.php' style='color: blue;'>🔐 Go to Quick Login</a></p>";
    echo "<p><a href='index.php' style='color: blue;'>🏠 Go to Home Page</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
