# FestivalKit - Complete Setup Guide

## 🚀 Quick Start

### Prerequisites
- PHP 7.4+ with GD and ZIP extensions
- MySQL 5.7+ or MariaDB
- Web server (Apache/Nginx)
- Composer (optional, for future dependencies)

### Installation Steps

1. **Database Setup**
   ```bash
   # Create database and import schema
   mysql -u root -p -e "CREATE DATABASE festivalkit;"
   mysql -u root -p festivalkit < database_schema.sql
   ```

2. **Configuration**
   ```bash
   # Update database credentials
   nano config/database.php
   
   # Set proper permissions
   chmod 755 uploads/
   chmod 755 uploads/logos/
   chmod 755 uploads/templates/
   chmod 755 uploads/generated/
   ```

3. **Generate Sample Images**
   ```bash
   php create_sample_images.php
   ```

4. **Test Installation**
   - Visit your domain/subdomain
   - Admin panel: `/admin/` (admin/admin123)

## 🧪 Testing Checklist

### User Flow Testing

#### Registration & Login
- [ ] User can register with valid email/password
- [ ] Email validation works
- [ ] Password strength requirements enforced
- [ ] Login with correct credentials works
- [ ] Login lockout after failed attempts
- [ ] Session management works correctly

#### Free Image Generator
- [ ] Festival selection loads templates
- [ ] Logo upload validates file types/sizes
- [ ] Image generation works with logo overlay
- [ ] Custom message appears on generated image
- [ ] Download link works correctly
- [ ] Generated images are properly sized (1200x800)

#### Paid Kit Generator
- [ ] Business information form validation
- [ ] Logo upload and previous logo reuse
- [ ] Payment simulation works
- [ ] ZIP file generation includes all festivals
- [ ] Download tracking increments correctly
- [ ] Payment logging works

#### Premium Features
- [ ] Social media connection simulation
- [ ] Post scheduling functionality
- [ ] Scheduled posts display correctly
- [ ] Post deletion works

### Admin Panel Testing

#### Authentication
- [ ] Admin login with correct credentials
- [ ] Admin session management
- [ ] Admin logout functionality

#### Festival Management
- [ ] Add new festivals
- [ ] Edit existing festivals
- [ ] Delete festivals (with confirmation)
- [ ] Featured festival toggle
- [ ] Festival listing and pagination

#### Template Management
- [ ] Upload new templates
- [ ] Associate templates with festivals
- [ ] Template activation/deactivation
- [ ] Template deletion removes files
- [ ] Template filtering by festival

#### User Management
- [ ] User listing with statistics
- [ ] User package type updates
- [ ] User detail view with activity
- [ ] User search and filtering

#### Payment Management
- [ ] Payment listing with filters
- [ ] Payment status display
- [ ] CSV export functionality
- [ ] Payment statistics accuracy

#### Settings
- [ ] Setting updates save correctly
- [ ] System information displays
- [ ] Feature toggles work
- [ ] Price updates reflect in frontend

### Security Testing

#### Input Validation
- [ ] SQL injection prevention (prepared statements)
- [ ] XSS prevention (htmlspecialchars)
- [ ] CSRF token validation
- [ ] File upload validation
- [ ] Path traversal prevention

#### Authentication Security
- [ ] Password hashing (password_hash)
- [ ] Session regeneration on login
- [ ] Proper session timeout
- [ ] Admin/user session separation

#### File Security
- [ ] Upload directory permissions
- [ ] File type validation
- [ ] File size limits
- [ ] Secure file downloads

## 🔧 Configuration Options

### Environment Settings
```php
// config/config.php
define('DEBUG_MODE', 0); // Set to 0 for production
define('APP_URL', 'https://yourdomain.com');
```

### Database Optimization
```sql
-- Add indexes for better performance
ALTER TABLE users ADD INDEX idx_email (email);
ALTER TABLE payments ADD INDEX idx_user_status (user_id, status);
ALTER TABLE templates ADD INDEX idx_festival_active (festival_id, is_active);
```

### File Permissions
```bash
# Secure permissions
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
chmod 600 config/database.php
```

## 🐛 Troubleshooting

### Common Issues

#### GD Extension Missing
```bash
# Ubuntu/Debian
sudo apt-get install php-gd

# CentOS/RHEL
sudo yum install php-gd
```

#### Upload Issues
- Check `upload_max_filesize` in php.ini
- Verify directory permissions
- Check disk space

#### Database Connection
- Verify credentials in config/database.php
- Check MySQL service status
- Ensure database exists

#### Image Generation Fails
- Verify GD extension is loaded
- Check template file permissions
- Ensure sufficient memory limit

### Error Logs
```bash
# Check PHP error logs
tail -f /var/log/php_errors.log

# Check web server logs
tail -f /var/log/apache2/error.log
tail -f /var/log/nginx/error.log
```

## 📊 Performance Optimization

### PHP Configuration
```ini
; php.ini optimizations
memory_limit = 256M
max_execution_time = 60
upload_max_filesize = 10M
post_max_size = 10M
```

### Database Optimization
```sql
-- Optimize tables
OPTIMIZE TABLE users, festivals, templates, payments;

-- Add missing indexes
CREATE INDEX idx_created_at ON payments(created_at);
CREATE INDEX idx_festival_date ON festivals(date);
```

### Caching (Future Enhancement)
- Implement Redis/Memcached for session storage
- Cache generated images
- Use CDN for static assets

## 🔒 Security Hardening

### Production Security
1. **Disable Debug Mode**
   ```php
   define('DEBUG_MODE', 0);
   ```

2. **Secure Headers**
   ```apache
   # .htaccess
   Header always set X-Content-Type-Options nosniff
   Header always set X-Frame-Options DENY
   Header always set X-XSS-Protection "1; mode=block"
   ```

3. **File Permissions**
   ```bash
   chmod 600 config/*.php
   chmod 755 uploads/
   ```

4. **Database Security**
   - Use strong database passwords
   - Limit database user privileges
   - Enable SSL connections

## 📈 Monitoring & Analytics

### Key Metrics to Track
- User registrations per day
- Image generations per user
- Kit purchase conversion rate
- Payment success/failure rates
- Template usage statistics

### Log Analysis
```bash
# Analyze access patterns
grep "POST /generate_image.php" access.log | wc -l
grep "POST /generate_kit.php" access.log | wc -l
```

## 🚀 Deployment

### Production Deployment
1. **Environment Setup**
   - Configure web server
   - Set up SSL certificate
   - Configure database

2. **Code Deployment**
   ```bash
   # Upload files
   rsync -avz --exclude='.git' ./ user@server:/var/www/festivalkit/
   
   # Set permissions
   chown -R www-data:www-data /var/www/festivalkit/
   ```

3. **Database Migration**
   ```bash
   mysql -u username -p festivalkit < database_schema.sql
   ```

4. **Final Checks**
   - Test all functionality
   - Verify SSL certificate
   - Check error logs

## 📞 Support

### Getting Help
- Check error logs first
- Review this setup guide
- Test with sample data
- Verify server requirements

### Common Solutions
- Clear browser cache
- Check file permissions
- Restart web server
- Verify database connection

---

**Note**: This is a demonstration application. For production use, implement additional security measures, monitoring, and backup strategies.
