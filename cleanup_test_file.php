<?php
require_once 'config/config.php';

$filename = $_GET['file'] ?? '';

if (empty($filename)) {
    echo "No file specified";
    exit;
}

// Security check
if (strpos($filename, '..') !== false || strpos($filename, '/') !== false) {
    echo "Invalid filename";
    exit;
}

// Only allow cleanup of debug files
if (strpos($filename, 'debug_generated_') !== 0) {
    echo "Only debug files can be cleaned up";
    exit;
}

$file_path = ROOT_PATH . '/' . UPLOAD_DIR_GENERATED . $filename;

if (file_exists($file_path)) {
    if (unlink($file_path)) {
        echo "File deleted successfully";
    } else {
        echo "Failed to delete file";
    }
} else {
    echo "File not found";
}
?>
