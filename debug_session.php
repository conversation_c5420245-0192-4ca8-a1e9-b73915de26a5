<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

echo "<h2>Session Debug Information</h2>";

echo "<h3>Session Data:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>Session Functions:</h3>";
echo "isLoggedIn(): " . (isLoggedIn() ? 'true' : 'false') . "<br>";
echo "getCurrentUserId(): " . (getCurrentUserId() ?? 'null') . "<br>";

if (getCurrentUserId()) {
    echo "<h3>User Data:</h3>";
    try {
        $user = getUserById($pdo, getCurrentUserId());
        if ($user) {
            echo "<pre>";
            print_r($user);
            echo "</pre>";
        } else {
            echo "User not found in database<br>";
        }
    } catch (Exception $e) {
        echo "Error getting user: " . $e->getMessage() . "<br>";
    }
}

echo "<h3>Database Connection:</h3>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as user_count FROM users");
    $result = $stmt->fetch();
    echo "Total users in database: " . $result['user_count'] . "<br>";
    
    if ($result['user_count'] > 0) {
        echo "<h4>Sample Users:</h4>";
        $stmt = $pdo->query("SELECT id, name, email, created_at FROM users LIMIT 5");
        $users = $stmt->fetchAll();
        echo "<pre>";
        print_r($users);
        echo "</pre>";
    }
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "<br>";
}

echo "<h3>Create Test User:</h3>";
echo '<form method="post">
    <input type="hidden" name="create_test_user" value="1">
    <button type="submit">Create Test User</button>
</form>';

if (isset($_POST['create_test_user'])) {
    try {
        // Check if test user already exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        
        if ($stmt->fetch()) {
            echo "<p style='color: orange;'>Test user already exists!</p>";
        } else {
            // Create test user
            $password_hash = password_hash('password123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (name, email, password, package_type, created_at) VALUES (?, ?, ?, ?, NOW())");
            $stmt->execute(['Test User', '<EMAIL>', $password_hash, 'free']);
            
            echo "<p style='color: green;'>Test user created successfully!</p>";
            echo "<p><strong>Email:</strong> <EMAIL></p>";
            echo "<p><strong>Password:</strong> password123</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error creating test user: " . $e->getMessage() . "</p>";
    }
}

echo "<h3>Login Test User:</h3>";
echo '<form method="post">
    <input type="hidden" name="login_test_user" value="1">
    <button type="submit">Login as Test User</button>
</form>';

if (isset($_POST['login_test_user'])) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $user = $stmt->fetch();
        
        if ($user) {
            loginUser($user['id'], $user['name'], $user['email']);
            echo "<p style='color: green;'>Logged in as test user!</p>";
            echo "<script>setTimeout(function(){ location.reload(); }, 1000);</script>";
        } else {
            echo "<p style='color: red;'>Test user not found!</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error logging in: " . $e->getMessage() . "</p>";
    }
}
?>
