<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    redirect('dashboard.php');
}

$errors = [];
$login_attempts = $_SESSION['login_attempts'] ?? 0;
$last_attempt_time = $_SESSION['last_attempt_time'] ?? 0;

// Check if user is locked out
$lockout_remaining = 0;
if ($login_attempts >= MAX_LOGIN_ATTEMPTS) {
    $lockout_remaining = LOGIN_LOCKOUT_TIME - (time() - $last_attempt_time);
    if ($lockout_remaining <= 0) {
        // Reset attempts after lockout period
        unset($_SESSION['login_attempts']);
        unset($_SESSION['last_attempt_time']);
        $login_attempts = 0;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && $lockout_remaining <= 0) {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid request. Please try again.';
    } else {
        // Get and sanitize input
        $email = sanitize_input($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        $remember_me = isset($_POST['remember_me']);
        
        // Validation
        if (empty($email)) {
            $errors[] = 'Email is required.';
        } elseif (!validate_email($email)) {
            $errors[] = 'Please enter a valid email address.';
        }
        
        if (empty($password)) {
            $errors[] = 'Password is required.';
        }
        
        // Authenticate user if no errors
        if (empty($errors)) {
            $user = getUserByEmail($pdo, $email);
            
            if ($user && verify_password($password, $user['password'])) {
                // Successful login
                loginUser($user['id'], $user['name'], $user['email']);
                
                // Reset login attempts
                unset($_SESSION['login_attempts']);
                unset($_SESSION['last_attempt_time']);
                
                // Set remember me cookie if requested
                if ($remember_me) {
                    $token = bin2hex(random_bytes(32));
                    // In a real application, store this token in database
                    setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 days
                }
                
                // Redirect to dashboard or intended page
                $redirect_url = $_GET['redirect'] ?? 'dashboard.php';
                redirect($redirect_url);
            } else {
                // Failed login
                $errors[] = 'Invalid email or password.';
                
                // Increment login attempts
                $_SESSION['login_attempts'] = $login_attempts + 1;
                $_SESSION['last_attempt_time'] = time();
            }
        }
    }
}

// Display flash messages
$flash = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="<?php echo getAssetUrl('css/style.css'); ?>" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow-lg border-0 mt-5">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <h2 class="mb-0">
                            <a href="index.php" class="text-white text-decoration-none">
                                🎉 <?php echo APP_NAME; ?>
                            </a>
                        </h2>
                        <p class="mb-0">Welcome back!</p>
                    </div>
                    <div class="card-body p-5">
                        <?php if ($flash): ?>
                            <div class="alert alert-<?php echo $flash['type'] === 'error' ? 'danger' : $flash['type']; ?>">
                                <?php echo htmlspecialchars($flash['message']); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($lockout_remaining > 0): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-lock me-2"></i>
                                Too many failed login attempts. Please try again in 
                                <span id="lockout-timer"><?php echo $lockout_remaining; ?></span> seconds.
                            </div>
                        <?php else: ?>
                            <form method="POST" action="">
                                <?php echo getCSRFTokenField(); ?>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                           required autofocus>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                                    <label class="form-check-label" for="remember_me">
                                        Remember me for 30 days
                                    </label>
                                </div>
                                
                                <?php if ($login_attempts > 0): ?>
                                    <div class="alert alert-info">
                                        <small>
                                            Failed attempts: <?php echo $login_attempts; ?>/<?php echo MAX_LOGIN_ATTEMPTS; ?>
                                        </small>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-sign-in-alt me-2"></i>Login
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <p class="mb-2">Don't have an account?</p>
                            <a href="register.php" class="btn btn-outline-primary">
                                <i class="fas fa-user-plus me-2"></i>Create Account
                            </a>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="#" class="text-muted small">Forgot your password?</a>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <a href="index.php" class="text-muted">
                        <i class="fas fa-arrow-left me-2"></i>Back to Homepage
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
    
    <?php if ($lockout_remaining > 0): ?>
    <script>
        // Countdown timer for lockout
        var timeLeft = <?php echo $lockout_remaining; ?>;
        var timer = setInterval(function() {
            timeLeft--;
            document.getElementById('lockout-timer').textContent = timeLeft;
            
            if (timeLeft <= 0) {
                clearInterval(timer);
                location.reload();
            }
        }, 1000);
    </script>
    <?php endif; ?>
</body>
</html>
