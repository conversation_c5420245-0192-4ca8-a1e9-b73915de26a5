<?php
session_start();
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

// Create a test session if not logged in
if (!isLoggedIn()) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'Test User';
    $_SESSION['user_email'] = '<EMAIL>';
}

echo "<h1>Final Generation Test</h1>";

// Get first festival and template
try {
    $festivals = getAllFestivals($pdo);
    $festival = $festivals[0];
    $templates = getTemplatesByFestival($pdo, $festival['id']);
    $template = $templates[0];
    
    echo "<h2>Test Data</h2>";
    echo "Festival: {$festival['name']} (ID: {$festival['id']})<br>";
    echo "Template: {$template['name']} (ID: {$template['id']})<br>";
    echo "Template Path: {$template['image_path']}<br>";
    
    // Check if template file exists
    if (file_exists($template['image_path'])) {
        echo "✅ Template file exists<br>";
    } else {
        echo "❌ Template file missing<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
    exit;
}

$csrf_token = generateCSRFToken();
?>

<h2>Test Form</h2>
<form id="test-form">
    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
    <input type="hidden" name="festival_id" value="<?php echo $festival['id']; ?>">
    <input type="hidden" name="template_id" value="<?php echo $template['id']; ?>">
    
    <div style="margin: 10px 0;">
        <label>Message:</label><br>
        <input type="text" name="message" value="Test Message - <?php echo date('Y-m-d H:i:s'); ?>" style="width: 300px;">
    </div>
    
    <div style="margin: 10px 0;">
        <label>Logo (optional):</label><br>
        <input type="file" name="logo" accept="image/*">
    </div>
    
    <button type="submit" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px;">
        Generate Image
    </button>
</form>

<div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc; display: none;">
    <h3>Result:</h3>
    <div id="result-content"></div>
</div>

<div id="preview" style="margin-top: 20px; display: none;">
    <h3>Preview:</h3>
    <img id="preview-image" style="max-width: 400px; border: 1px solid #ccc;">
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    $('#test-form').submit(function(e) {
        e.preventDefault();
        
        $('#result').show();
        $('#result-content').html('Generating...');
        $('#preview').hide();
        
        var formData = new FormData(this);
        
        $.ajax({
            url: 'generate_image.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                console.log('Success response:', response);
                
                if (response.success) {
                    $('#result-content').html(
                        '<div style="color: green;">✅ ' + response.message + '</div>' +
                        '<p>Image URL: <a href="' + response.image_url + '" target="_blank">' + response.image_url + '</a></p>' +
                        '<p>Download: <a href="' + response.download_url + '" target="_blank">Download Image</a></p>'
                    );
                    
                    // Show preview
                    $('#preview-image').attr('src', response.image_url + '?t=' + Date.now());
                    $('#preview').show();
                } else {
                    $('#result-content').html('<div style="color: red;">❌ ' + response.message + '</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error response:', xhr.responseText);
                $('#result-content').html(
                    '<div style="color: red;">❌ AJAX Error</div>' +
                    '<p>Status: ' + xhr.status + '</p>' +
                    '<p>Error: ' + error + '</p>' +
                    '<pre>' + xhr.responseText + '</pre>'
                );
            }
        });
    });
});
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
form { background: #f5f5f5; padding: 15px; border-radius: 5px; }
</style>

<p><a href="free_generator.php">→ Go to Free Generator</a></p>
<p><a href="index.php">← Back to Homepage</a></p>
