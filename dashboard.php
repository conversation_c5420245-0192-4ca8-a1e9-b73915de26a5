<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

// Require user login
requireLogin();

$user_id = getCurrentUserId();
$user = getUserById($pdo, $user_id);

// Initialize variables to prevent warnings
$total_downloads = 0;
$total_payments = 0;
$scheduled_posts = 0;
$recent_downloads = [];
$upcoming_festivals = [];
$user_upload = null;

try {
    // Get user statistics with error handling
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_downloads FROM kit_downloads WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $result = $stmt->fetch();
    $total_downloads = $result ? intval($result['total_downloads']) : 0;

    $stmt = $pdo->prepare("SELECT COUNT(*) as total_payments FROM payments WHERE user_id = ? AND status = 'completed'");
    $stmt->execute([$user_id]);
    $result = $stmt->fetch();
    $total_payments = $result ? intval($result['total_payments']) : 0;

    $stmt = $pdo->prepare("SELECT COUNT(*) as scheduled_posts FROM scheduled_posts WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $result = $stmt->fetch();
    $scheduled_posts = $result ? intval($result['scheduled_posts']) : 0;

    // Get recent downloads
    $stmt = $pdo->prepare("SELECT * FROM kit_downloads WHERE user_id = ? ORDER BY created_at DESC LIMIT 5");
    $stmt->execute([$user_id]);
    $recent_downloads = $stmt->fetchAll() ?: [];

    // Get upcoming festivals
    $upcoming_festivals = getFeaturedFestivals($pdo, 3) ?: [];

    // Get user uploads
    $stmt = $pdo->prepare("SELECT * FROM user_uploads WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
    $stmt->execute([$user_id]);
    $user_upload = $stmt->fetch();

} catch (Exception $e) {
    // Log error but don't show to user
    error_log("Dashboard error: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            min-height: 100vh;
            color: #333;
        }

        .dashboard-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .modern-navbar {
            background: rgba(255, 255, 255, 0.15) !important;
            backdrop-filter: blur(20px);
            border: none !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
            padding: 15px 0 !important;
        }

        .navbar-brand {
            font-weight: 800 !important;
            font-size: 1.8rem !important;
            color: white !important;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500 !important;
            padding: 10px 20px !important;
            border-radius: 25px !important;
            transition: all 0.3s ease !important;
        }

        .nav-link:hover, .nav-link.active {
            background: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            transform: translateY(-2px);
        }

        .welcome-hero {
            background: rgba(255, 255, 255, 0.15) !important;
            backdrop-filter: blur(20px);
            border-radius: 30px !important;
            padding: 60px 40px !important;
            text-align: center;
            margin-bottom: 40px !important;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .welcome-hero h1 {
            color: white !important;
            font-weight: 800 !important;
            font-size: 3.5rem !important;
            margin-bottom: 20px !important;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .welcome-hero p {
            color: rgba(255, 255, 255, 0.9) !important;
            font-size: 1.3rem !important;
            font-weight: 400 !important;
        }

        .stats-card {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border-radius: 25px !important;
            padding: 40px 30px !important;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
            height: 100%;
        }

        .stats-card:hover {
            transform: translateY(-15px) scale(1.05) !important;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2) !important;
        }

        .stats-card h3 {
            font-size: 3.5rem !important;
            font-weight: 800 !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 15px !important;
        }

        .stats-card p {
            color: #666 !important;
            font-weight: 600 !important;
            font-size: 1.1rem !important;
        }

        .stats-card i {
            font-size: 3rem !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px !important;
        }

        .action-card {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border-radius: 25px !important;
            padding: 40px 30px !important;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
            height: 100%;
        }

        .action-card:hover {
            transform: translateY(-20px) scale(1.05) !important;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2) !important;
        }

        .action-card h5 {
            font-weight: 700 !important;
            font-size: 1.5rem !important;
            color: #333 !important;
            margin-bottom: 20px !important;
        }

        .action-card p {
            color: #666 !important;
            font-size: 1rem !important;
            line-height: 1.6 !important;
            margin-bottom: 30px !important;
        }

        .action-icon {
            width: 100px !important;
            height: 100px !important;
            border-radius: 50% !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            margin: 0 auto 30px !important;
            font-size: 2.5rem !important;
            color: white !important;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4) !important;
            transition: all 0.3s ease !important;
        }

        .action-card:hover .action-icon {
            transform: scale(1.1) rotate(5deg) !important;
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6) !important;
        }

        .btn-modern {
            border-radius: 50px !important;
            padding: 15px 35px !important;
            font-weight: 700 !important;
            font-size: 1.1rem !important;
            text-transform: uppercase !important;
            letter-spacing: 1px !important;
            border: none !important;
            transition: all 0.3s ease !important;
            position: relative !important;
            overflow: hidden !important;
        }

        .btn-primary.btn-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4) !important;
        }

        .btn-primary.btn-modern:hover {
            transform: translateY(-3px) !important;
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6) !important;
        }

        .btn-success.btn-modern {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
            box-shadow: 0 10px 30px rgba(17, 153, 142, 0.4) !important;
        }

        .btn-success.btn-modern:hover {
            transform: translateY(-3px) !important;
            box-shadow: 0 15px 40px rgba(17, 153, 142, 0.6) !important;
        }

        .modern-card {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border-radius: 25px !important;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.3s ease !important;
        }

        .modern-card:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
        }

        .section-title {
            color: white !important;
            font-weight: 800 !important;
            font-size: 2.5rem !important;
            text-align: center !important;
            margin-bottom: 50px !important;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        @media (max-width: 768px) {
            .welcome-hero h1 {
                font-size: 2.5rem !important;
            }
            .welcome-hero {
                padding: 40px 20px !important;
            }
            .stats-card, .action-card {
                margin-bottom: 20px !important;
            }
        }

        .stats-card-modern h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .stats-card-modern p {
            font-size: 1rem;
            font-weight: 500;
            opacity: 0.9;
            margin-bottom: 0;
        }

        .welcome-hero {
            background: var(--primary-gradient);
            color: white;
            border-radius: 25px;
            padding: 3rem 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .welcome-hero::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .action-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            border: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            height: 100%;
        }

        .action-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .action-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: white;
        }

        .btn-modern {
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            border: none;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .btn-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-modern:hover::before {
            left: 100%;
        }

        .activity-item {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .activity-item:hover {
            transform: translateX(5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        .festival-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border-left: 4px solid var(--success-color);
        }

        .festival-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
        }
    </style>
</head>
<body>
    <!-- Ultra Modern Navigation -->
    <nav class="navbar navbar-expand-lg modern-navbar">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-sparkles me-2"></i><?php echo APP_NAME; ?>
            </a>
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <i class="fas fa-bars text-white"></i>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="free_generator.php">
                            <i class="fas fa-magic me-2"></i>Free Generator
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="kit_generator.php">
                            <i class="fas fa-box me-2"></i>Kit Generator
                        </a>
                    </li>
                    <?php if (($user['package_type'] ?? '') === 'premium'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="social_posting.php">
                            <i class="fas fa-share-alt me-2"></i>Auto Posting
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="template_designer.php">
                            <i class="fas fa-paint-brush me-2"></i>Template Designer
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-2"></i><?php echo htmlspecialchars($user['name'] ?? 'User'); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="dashboard-container">
        <?php displayFlashMessage(); ?>

        <!-- Ultra Modern Welcome Hero -->
        <div class="welcome-hero">
            <h1>
                Welcome back, <?php echo htmlspecialchars($user['name'] ?? 'User'); ?>!
                <i class="fas fa-sparkles"></i>
            </h1>
            <p>Ready to create some amazing festival content?</p>
            <div class="d-flex justify-content-center gap-3 flex-wrap mt-4">
                <span class="badge bg-light bg-opacity-25 text-white fs-6 px-3 py-2 rounded-pill">
                    <i class="fas fa-crown me-2"></i><?php echo ucfirst($user['package_type'] ?? 'free'); ?> Account
                </span>
                <span class="badge bg-light bg-opacity-25 text-white fs-6 px-3 py-2 rounded-pill">
                    <i class="fas fa-calendar me-2"></i>Member since <?php echo date('M Y', strtotime($user['created_at'] ?? 'now')); ?>
                </span>
            </div>
        </div>

        <!-- Ultra Modern Statistics Cards -->
        <div class="row mb-5">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <i class="fas fa-download"></i>
                    <h3><?php echo number_format($total_downloads); ?></h3>
                    <p>Total Downloads</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <i class="fas fa-credit-card"></i>
                    <h3><?php echo number_format($total_payments); ?></h3>
                    <p>Completed Purchases</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <i class="fas fa-calendar"></i>
                    <h3><?php echo number_format($scheduled_posts); ?></h3>
                    <p>Scheduled Posts</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stats-card">
                    <i class="fas fa-star"></i>
                    <h3><?php echo date('M j', strtotime($user['created_at'] ?? 'now')); ?></h3>
                    <p>Member Since</p>
                </div>
            </div>
        </div>

        <!-- Ultra Modern Quick Actions -->
        <h2 class="section-title">Quick Actions</h2>
        <div class="row mb-5">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-magic"></i>
                    </div>
                    <h5>Free Generator</h5>
                    <p>Create a single festival image with your logo and custom message.</p>
                    <a href="free_generator.php" class="btn btn-primary btn-modern">
                        <i class="fas fa-play me-2"></i>Start Creating
                    </a>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <h5>Kit Generator</h5>
                    <p>Generate complete festival image sets and download as ZIP.</p>
                    <a href="kit_generator.php" class="btn btn-success btn-modern">
                        <i class="fas fa-rocket me-2"></i>Generate Kit
                    </a>
                    <div class="mt-3">
                        <small style="color: #666; font-weight: 600;">
                            <?php
                            try {
                                echo formatCurrency(getSetting($pdo, 'kit_price', DEFAULT_KIT_PRICE));
                            } catch (Exception $e) {
                                echo '$10.00';
                            }
                            ?>
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-share-alt"></i>
                    </div>
                    <h5>Auto Posting</h5>
                    <p>Schedule automatic social media posts for festivals.</p>
                    <?php if (($user['package_type'] ?? '') === 'premium'): ?>
                        <a href="social_posting.php" class="btn btn-primary btn-modern">
                            <i class="fas fa-calendar-plus me-2"></i>Manage Posts
                        </a>
                    <?php else: ?>
                        <a href="upgrade.php" class="btn btn-primary btn-modern" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;">
                            <i class="fas fa-crown me-2"></i>Upgrade to Premium
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-paint-brush"></i>
                    </div>
                    <h5>Template Designer</h5>
                    <p>Create custom festival templates with our advanced design tools.</p>
                    <?php if (($user['package_type'] ?? '') === 'premium'): ?>
                        <a href="template_designer.php" class="btn btn-primary btn-modern">
                            <i class="fas fa-palette me-2"></i>Design Templates
                        </a>
                    <?php else: ?>
                        <a href="upgrade.php" class="btn btn-primary btn-modern" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;">
                            <i class="fas fa-crown me-2"></i>Upgrade to Premium
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Ultra Modern Activity Section -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="modern-card">
                    <div class="card-body p-4">
                        <h5 style="color: #333; font-weight: 700; margin-bottom: 30px;">
                            <i class="fas fa-history me-3" style="color: #667eea;"></i>Recent Activity
                        </h5>

                        <?php if (empty($recent_downloads)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-download fa-3x mb-3" style="color: #ccc;"></i>
                                <p style="color: #666;">No downloads yet</p>
                                <a href="free_generator.php" class="btn btn-primary btn-modern">Start Creating</a>
                            </div>
                        <?php else: ?>
                            <?php foreach ($recent_downloads as $download): ?>
                                <div style="background: #f8f9ff; padding: 20px; border-radius: 15px; margin-bottom: 15px; border-left: 4px solid #667eea;">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 style="color: #333; font-weight: 600; margin-bottom: 5px;">
                                                <i class="fas fa-box me-2" style="color: #667eea;"></i>Festival Kit
                                            </h6>
                                            <small style="color: #666;">
                                                <?php echo date('M j, Y g:i A', strtotime($download['created_at'] ?? 'now')); ?>
                                            </small>
                                        </div>
                                        <span class="badge" style="background: #667eea; color: white; padding: 8px 15px; border-radius: 20px;">
                                            <?php echo intval($download['download_count'] ?? 0); ?> downloads
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="modern-card">
                    <div class="card-body p-4">
                        <h5 style="color: #333; font-weight: 700; margin-bottom: 30px;">
                            <i class="fas fa-calendar-alt me-3" style="color: #11998e;"></i>Upcoming Festivals
                        </h5>

                        <?php if (empty($upcoming_festivals)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-calendar-times fa-3x mb-3" style="color: #ccc;"></i>
                                <p style="color: #666;">No upcoming festivals</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($upcoming_festivals as $festival): ?>
                                <div style="background: #f0fffe; padding: 20px; border-radius: 15px; margin-bottom: 15px; border-left: 4px solid #11998e;">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 style="color: #333; font-weight: 600; margin-bottom: 8px;">
                                                <i class="fas fa-star me-2" style="color: #ffc107;"></i>
                                                <?php echo htmlspecialchars($festival['name'] ?? 'Festival'); ?>
                                            </h6>
                                            <p style="color: #666; font-size: 0.9rem; margin-bottom: 0;">
                                                <?php echo htmlspecialchars(substr($festival['description'] ?? '', 0, 80)); ?>
                                                <?php if (strlen($festival['description'] ?? '') > 80): ?>...<?php endif; ?>
                                            </p>
                                        </div>
                                        <span class="badge" style="background: #e9ecef; color: #333; padding: 8px 15px; border-radius: 20px;">
                                            <?php echo date('M j', strtotime($festival['date'] ?? 'now')); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Profile Summary -->
        <?php if ($user_upload): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0"><i class="fas fa-user-circle me-2"></i>Your Business Profile</h5>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <?php if ($user_upload['logo_path']): ?>
                                    <img src="<?php echo getUploadUrl($user_upload['logo_path']); ?>" 
                                         alt="Logo" class="img-fluid rounded" style="max-height: 80px;">
                                <?php else: ?>
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                         style="height: 80px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-10">
                                <h6 class="mb-1"><?php echo htmlspecialchars($user_upload['tagline'] ?: 'No tagline set'); ?></h6>
                                <p class="mb-0 text-muted small">
                                    <?php echo htmlspecialchars($user_upload['contact_info'] ?: 'No contact info set'); ?>
                                </p>
                                <small class="text-muted">
                                    Updated: <?php echo date('M j, Y', strtotime($user_upload['created_at'])); ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <!-- Ultra Modern Footer -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="modern-card text-center">
                    <div class="card-body py-5">
                        <h4 style="color: #333; font-weight: 700; margin-bottom: 20px;">Need Help Getting Started?</h4>
                        <p style="color: #666; font-size: 1.1rem; margin-bottom: 30px;">Check out our quick tutorial or contact support for assistance.</p>
                        <div class="d-flex justify-content-center gap-3 flex-wrap">
                            <a href="#" class="btn btn-primary btn-modern">
                                <i class="fas fa-play-circle me-2"></i>Watch Tutorial
                            </a>
                            <a href="#" class="btn btn-primary btn-modern" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;">
                                <i class="fas fa-question-circle me-2"></i>Get Support
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>

    <script>
        // Modern dashboard interactions
        $(document).ready(function() {
            // Add smooth scrolling
            $('a[href^="#"]').on('click', function(event) {
                var target = $(this.getAttribute('href'));
                if( target.length ) {
                    event.preventDefault();
                    $('html, body').stop().animate({
                        scrollTop: target.offset().top - 100
                    }, 1000);
                }
            });

            // Add loading states to buttons
            $('.btn-modern').on('click', function() {
                var $btn = $(this);
                var originalText = $btn.html();

                $btn.html('<i class="fas fa-spinner fa-spin me-2"></i>Loading...');
                $btn.prop('disabled', true);

                // Re-enable after 2 seconds (for demo)
                setTimeout(function() {
                    $btn.html(originalText);
                    $btn.prop('disabled', false);
                }, 2000);
            });

            // Add hover effects to cards
            $('.modern-card, .action-card, .stats-card-modern').hover(
                function() {
                    $(this).addClass('shadow-lg');
                },
                function() {
                    $(this).removeClass('shadow-lg');
                }
            );

            // Auto-hide flash messages after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
        });
    </script>
</body>
</html>
