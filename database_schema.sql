-- FestivalKit Database Schema
CREATE DATABASE IF NOT EXISTS festivalkit;
USE festivalkit;

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(150) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    package_type ENUM('free', 'paid', 'premium') DEFAULT 'free',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Festivals table
CREATE TABLE festivals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    date DATE NOT NULL,
    description TEXT,
    is_featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Templates table
CREATE TABLE templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    festival_id INT NOT NULL,
    image_path VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (festival_id) REFERENCES festivals(id) ON DELETE CASCADE
);

-- User uploads table
CREATE TABLE user_uploads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    logo_path VARCHAR(255),
    tagline VARCHAR(255),
    contact_info TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Kit downloads table
CREATE TABLE kit_downloads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    generated_zip_path VARCHAR(255) NOT NULL,
    download_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Payments table
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(50),
    transaction_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Scheduled posts table
CREATE TABLE scheduled_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    festival_id INT NOT NULL,
    post_date DATE NOT NULL,
    status ENUM('scheduled', 'posted', 'failed') DEFAULT 'scheduled',
    social_platform VARCHAR(50),
    image_path VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (festival_id) REFERENCES festivals(id) ON DELETE CASCADE
);

-- Admin users table
CREATE TABLE admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(150) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Settings table
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default admin user (password: admin123)
INSERT INTO admin_users (username, password, email) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>');

-- Insert default settings
INSERT INTO settings (setting_key, setting_value) VALUES 
('kit_price', '10.00'),
('watermark_enabled', '1'),
('site_name', 'FestivalKit'),
('max_logo_size', '5242880');

-- Insert sample festivals
INSERT INTO festivals (name, date, description, is_featured) VALUES 
('New Year 2024', '2024-01-01', 'Celebrate the New Year with beautiful designs', TRUE),
('Valentine\'s Day', '2024-02-14', 'Spread love with romantic festival images', TRUE),
('Eid al-Fitr', '2024-04-10', 'Celebrate the end of Ramadan', TRUE),
('Christmas', '2024-12-25', 'Merry Christmas celebrations', TRUE),
('Diwali', '2024-11-01', 'Festival of Lights', TRUE),
('Halloween', '2024-10-31', 'Spooky Halloween celebrations', FALSE);

-- Insert sample templates (you'll need to add actual image files)
INSERT INTO templates (festival_id, image_path, name) VALUES 
(1, 'uploads/templates/newyear_template1.jpg', 'New Year Gold'),
(1, 'uploads/templates/newyear_template2.jpg', 'New Year Silver'),
(2, 'uploads/templates/valentine_template1.jpg', 'Valentine Hearts'),
(3, 'uploads/templates/eid_template1.jpg', 'Eid Mubarak'),
(4, 'uploads/templates/christmas_template1.jpg', 'Christmas Joy'),
(5, 'uploads/templates/diwali_template1.jpg', 'Diwali Lights');

-- Create user templates table for pro users (Konva.js designer)
CREATE TABLE user_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    festival_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    template_data LONGTEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (festival_id) REFERENCES festivals(id) ON DELETE CASCADE
);
