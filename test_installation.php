<?php
/**
 * FestivalKit Installation Test Script
 * Run this script to verify your installation is working correctly
 */

// Prevent direct access in production
if (!defined('TESTING_MODE')) {
    define('TESTING_MODE', true);
}

// Load configuration
require_once 'config/config.php';

echo "<h1>FestivalKit Installation Test</h1>\n";
echo "<style>body{font-family:Arial,sans-serif;margin:40px;} .pass{color:green;} .fail{color:red;} .warning{color:orange;}</style>\n";

$tests_passed = 0;
$tests_failed = 0;
$warnings = 0;

function test_result($test_name, $result, $message = '') {
    global $tests_passed, $tests_failed;
    
    if ($result) {
        echo "<p class='pass'>✓ $test_name</p>\n";
        $tests_passed++;
    } else {
        echo "<p class='fail'>✗ $test_name" . ($message ? " - $message" : "") . "</p>\n";
        $tests_failed++;
    }
}

function test_warning($test_name, $message) {
    global $warnings;
    echo "<p class='warning'>⚠ $test_name - $message</p>\n";
    $warnings++;
}

echo "<h2>1. PHP Environment Tests</h2>\n";

// PHP Version
$php_version = phpversion();
test_result("PHP Version ($php_version)", version_compare($php_version, '7.4.0', '>='), 'Requires PHP 7.4+');

// Required Extensions
$required_extensions = ['gd', 'pdo', 'pdo_mysql', 'zip', 'json'];
foreach ($required_extensions as $ext) {
    test_result("Extension: $ext", extension_loaded($ext));
}

// PHP Configuration
$upload_max = ini_get('upload_max_filesize');
$post_max = ini_get('post_max_size');
$memory_limit = ini_get('memory_limit');

test_result("Upload Max Filesize ($upload_max)", (int)$upload_max >= 5, 'Should be at least 5M');
test_result("Post Max Size ($post_max)", (int)$post_max >= 10, 'Should be at least 10M');
test_result("Memory Limit ($memory_limit)", (int)$memory_limit >= 128, 'Should be at least 128M');

echo "<h2>2. File System Tests</h2>\n";

// Check if files exist
$required_files = [
    'config/database.php',
    'config/session.php',
    'config/config.php',
    'includes/functions.php',
    'assets/css/style.css',
    'assets/js/main.js',
    'index.php',
    'login.php',
    'register.php',
    'dashboard.php',
    'admin/index.php'
];

foreach ($required_files as $file) {
    test_result("File exists: $file", file_exists($file));
}

// Check directory permissions
$upload_dirs = [
    'uploads',
    'uploads/logos',
    'uploads/templates',
    'uploads/generated'
];

foreach ($upload_dirs as $dir) {
    $exists = is_dir($dir);
    $writable = $exists && is_writable($dir);
    
    test_result("Directory exists: $dir", $exists);
    if ($exists) {
        test_result("Directory writable: $dir", $writable);
    }
}

echo "<h2>3. Database Tests</h2>\n";

try {
    require_once 'config/database.php';
    test_result("Database connection", isset($pdo) && $pdo instanceof PDO);
    
    // Check if tables exist
    $required_tables = [
        'users', 'festivals', 'templates', 'user_uploads',
        'kit_downloads', 'payments', 'scheduled_posts',
        'admin_users', 'settings'
    ];
    
    foreach ($required_tables as $table) {
        try {
            $stmt = $pdo->query("SELECT 1 FROM $table LIMIT 1");
            test_result("Table exists: $table", true);
        } catch (PDOException $e) {
            test_result("Table exists: $table", false, $e->getMessage());
        }
    }
    
    // Check sample data
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM festivals");
        $count = $stmt->fetch()['count'];
        test_result("Sample festivals loaded", $count > 0, "Found $count festivals");
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_users");
        $count = $stmt->fetch()['count'];
        test_result("Admin user exists", $count > 0);
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM settings");
        $count = $stmt->fetch()['count'];
        test_result("Settings configured", $count > 0, "Found $count settings");
        
    } catch (PDOException $e) {
        test_result("Sample data check", false, $e->getMessage());
    }
    
} catch (Exception $e) {
    test_result("Database connection", false, $e->getMessage());
}

echo "<h2>4. Image Processing Tests</h2>\n";

if (extension_loaded('gd')) {
    $gd_info = gd_info();
    test_result("JPEG Support", $gd_info['JPEG Support']);
    test_result("PNG Support", $gd_info['PNG Support']);
    test_result("FreeType Support", $gd_info['FreeType Support']);
    
    // Test image creation
    try {
        $test_image = imagecreatetruecolor(100, 100);
        $color = imagecolorallocate($test_image, 255, 0, 0);
        imagefill($test_image, 0, 0, $color);
        
        $test_file = 'uploads/test_image.jpg';
        $result = imagejpeg($test_image, $test_file);
        imagedestroy($test_image);
        
        test_result("Image creation test", $result && file_exists($test_file));
        
        // Clean up
        if (file_exists($test_file)) {
            unlink($test_file);
        }
        
    } catch (Exception $e) {
        test_result("Image creation test", false, $e->getMessage());
    }
}

echo "<h2>5. Template Files Tests</h2>\n";

$template_files = [
    'uploads/templates/newyear_template1.jpg',
    'uploads/templates/valentine_template1.jpg',
    'uploads/templates/christmas_template1.jpg',
    'uploads/templates/diwali_template1.jpg'
];

foreach ($template_files as $template) {
    $exists = file_exists($template);
    test_result("Template exists: " . basename($template), $exists);
    
    if ($exists) {
        $size = getimagesize($template);
        test_result("Template is valid image: " . basename($template), $size !== false);
    }
}

echo "<h2>6. Security Tests</h2>\n";

// Check if sensitive files are protected
$sensitive_files = [
    'config/database.php',
    'database_schema.sql',
    'test_installation.php'
];

foreach ($sensitive_files as $file) {
    if (file_exists($file)) {
        test_warning("Sensitive file accessible", "$file should be protected in production");
    }
}

// Check debug mode
if (defined('DEBUG_MODE') && DEBUG_MODE) {
    test_warning("Debug mode enabled", "Should be disabled in production");
}

echo "<h2>7. Functionality Tests</h2>\n";

// Test core functions
try {
    require_once 'includes/functions.php';
    
    // Test sanitization
    $test_input = "<script>alert('test')</script>";
    $sanitized = sanitize_input($test_input);
    test_result("Input sanitization", $sanitized !== $test_input);
    
    // Test email validation
    test_result("Email validation (valid)", validate_email('<EMAIL>'));
    test_result("Email validation (invalid)", !validate_email('invalid-email'));
    
    // Test password hashing
    $password = 'test123';
    $hash = hash_password($password);
    test_result("Password hashing", !empty($hash) && $hash !== $password);
    test_result("Password verification", verify_password($password, $hash));
    
} catch (Exception $e) {
    test_result("Core functions test", false, $e->getMessage());
}

echo "<h2>8. Configuration Tests</h2>\n";

// Check configuration constants
$required_constants = [
    'APP_NAME', 'APP_VERSION', 'MAX_LOGO_SIZE',
    'UPLOAD_DIR_LOGOS', 'UPLOAD_DIR_TEMPLATES', 'UPLOAD_DIR_GENERATED'
];

foreach ($required_constants as $constant) {
    test_result("Constant defined: $constant", defined($constant));
}

// Test settings retrieval
if (isset($pdo)) {
    try {
        require_once 'includes/functions.php';
        $kit_price = getSetting($pdo, 'kit_price', 0);
        test_result("Settings retrieval", $kit_price > 0, "Kit price: $kit_price");
    } catch (Exception $e) {
        test_result("Settings retrieval", false, $e->getMessage());
    }
}

echo "<h2>Test Summary</h2>\n";
echo "<p><strong>Tests Passed:</strong> $tests_passed</p>\n";
echo "<p><strong>Tests Failed:</strong> $tests_failed</p>\n";
echo "<p><strong>Warnings:</strong> $warnings</p>\n";

if ($tests_failed == 0) {
    echo "<p class='pass'><strong>🎉 All tests passed! Your FestivalKit installation is ready.</strong></p>\n";
    echo "<p>You can now:</p>\n";
    echo "<ul>\n";
    echo "<li>Visit the <a href='index.php'>homepage</a></li>\n";
    echo "<li>Access the <a href='admin/'>admin panel</a> (admin/admin123)</li>\n";
    echo "<li>Register a new user account</li>\n";
    echo "<li>Test the image generation features</li>\n";
    echo "</ul>\n";
} else {
    echo "<p class='fail'><strong>❌ Some tests failed. Please fix the issues above before using FestivalKit.</strong></p>\n";
}

if ($warnings > 0) {
    echo "<p class='warning'><strong>⚠ Please review the warnings above for production deployment.</strong></p>\n";
}

echo "<hr>\n";
echo "<p><small>FestivalKit v" . (defined('APP_VERSION') ? APP_VERSION : '1.0.0') . " - Installation Test Complete</small></p>\n";
?>
