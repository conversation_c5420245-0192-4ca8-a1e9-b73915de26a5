<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if festival_id is provided
if (!isset($_GET['festival_id']) || empty($_GET['festival_id'])) {
    echo json_encode(['success' => false, 'message' => 'Festival ID is required.']);
    exit;
}

$festival_id = (int)$_GET['festival_id'];

try {
    // Get templates for the festival
    $templates = getTemplatesByFestival($pdo, $festival_id);
    
    // Format templates for JSON response
    $formatted_templates = [];
    foreach ($templates as $template) {
        $formatted_templates[] = [
            'id' => $template['id'],
            'name' => $template['name'],
            'image_url' => getUploadUrl($template['image_path'])
        ];
    }
    
    echo json_encode([
        'success' => true,
        'templates' => $formatted_templates
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Failed to load templates.'
    ]);
}
?>
