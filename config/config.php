<?php
// FestivalKit Configuration Constants

// Application Settings
define('APP_NAME', 'FestivalKit');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/festiveCreatives'); // Update this to your domain

// File Upload Settings
define('MAX_LOGO_SIZE', 5242880); // 5MB in bytes
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png']);
define('UPLOAD_DIR_LOGOS', 'uploads/logos/');
define('UPLOAD_DIR_TEMPLATES', 'uploads/templates/');
define('UPLOAD_DIR_GENERATED', 'uploads/generated/');

// Image Generation Settings
define('GENERATED_IMAGE_QUALITY', 90); // JPEG quality (1-100)
define('MAX_MESSAGE_LENGTH', 100);
define('LOGO_MAX_WIDTH_PERCENT', 20); // Logo max width as percentage of template
define('LOGO_MAX_HEIGHT_PERCENT', 20); // Logo max height as percentage of template

// Payment Settings
define('DEFAULT_KIT_PRICE', 10.00);
define('CURRENCY_SYMBOL', '$');
define('PAYMENT_METHODS', ['paypal', 'stripe']);

// Email Settings (for future implementation)
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'FestivalKit');

// Social Media Settings (mock integration)
define('SOCIAL_PLATFORMS', ['facebook', 'twitter', 'instagram', 'linkedin']);

// Admin Settings
define('ADMIN_EMAIL', '<EMAIL>');
define('ITEMS_PER_PAGE', 25);

// Security Settings
define('PASSWORD_MIN_LENGTH', 6);
define('SESSION_TIMEOUT', 3600); // 1 hour in seconds
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes in seconds

// File Paths
define('ROOT_PATH', dirname(__DIR__));
define('INCLUDES_PATH', ROOT_PATH . '/includes/');
define('CONFIG_PATH', ROOT_PATH . '/config/');
define('ADMIN_PATH', ROOT_PATH . '/admin/');
define('ASSETS_PATH', ROOT_PATH . '/assets/');

// URL Paths
define('BASE_URL', APP_URL);
define('ASSETS_URL', BASE_URL . '/assets');
define('UPLOADS_URL', BASE_URL . '/uploads');
define('ADMIN_URL', BASE_URL . '/admin');

// Error Reporting (set to 0 in production)
define('DEBUG_MODE', 1);
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Timezone
date_default_timezone_set('UTC');

// Create upload directories if they don't exist
$upload_dirs = [
    ROOT_PATH . '/' . UPLOAD_DIR_LOGOS,
    ROOT_PATH . '/' . UPLOAD_DIR_TEMPLATES,
    ROOT_PATH . '/' . UPLOAD_DIR_GENERATED
];

foreach ($upload_dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Autoload function for classes (if needed in future)
spl_autoload_register(function ($class_name) {
    $class_file = INCLUDES_PATH . 'classes/' . $class_name . '.php';
    if (file_exists($class_file)) {
        require_once $class_file;
    }
});

// Global error handler
function globalErrorHandler($errno, $errstr, $errfile, $errline) {
    if (!(error_reporting() & $errno)) {
        return false;
    }
    
    $error_message = "Error [$errno]: $errstr in $errfile on line $errline";
    
    if (DEBUG_MODE) {
        echo "<div class='alert alert-danger'>$error_message</div>";
    } else {
        // Log error to file in production
        error_log($error_message, 3, ROOT_PATH . '/logs/error.log');
    }
    
    return true;
}

set_error_handler('globalErrorHandler');

// Global exception handler
function globalExceptionHandler($exception) {
    $error_message = "Uncaught exception: " . $exception->getMessage() . 
                    " in " . $exception->getFile() . 
                    " on line " . $exception->getLine();
    
    if (DEBUG_MODE) {
        echo "<div class='alert alert-danger'>$error_message</div>";
    } else {
        error_log($error_message, 3, ROOT_PATH . '/logs/error.log');
        echo "<div class='alert alert-danger'>An error occurred. Please try again later.</div>";
    }
}

set_exception_handler('globalExceptionHandler');

// Helper function to get full URL
function getFullUrl($path = '') {
    return BASE_URL . '/' . ltrim($path, '/');
}

// Helper function to get asset URL
function getAssetUrl($path) {
    return ASSETS_URL . '/' . ltrim($path, '/');
}

// Helper function to get upload URL
function getUploadUrl($path) {
    return UPLOADS_URL . '/' . ltrim($path, '/');
}

// Helper function to check if running on HTTPS
function isHttps() {
    return (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') 
        || $_SERVER['SERVER_PORT'] == 443;
}

// Helper function to get current URL
function getCurrentUrl() {
    $protocol = isHttps() ? 'https://' : 'http://';
    return $protocol . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
}

// Helper function to redirect - moved to includes/functions.php

// Helper function to get client IP
function getClientIP() {
    $ip_keys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    foreach ($ip_keys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, 
                    FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}
?>
