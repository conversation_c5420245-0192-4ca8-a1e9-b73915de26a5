<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

// Require user login
requireLogin();

$user_id = getCurrentUserId();
$payment_id = $_GET['payment_id'] ?? null;

if (!$payment_id) {
    redirect('dashboard.php', 'Invalid payment reference.', 'error');
}

// Get payment details
$stmt = $pdo->prepare("SELECT * FROM payments WHERE id = ? AND user_id = ?");
$stmt->execute([$payment_id, $user_id]);
$payment = $stmt->fetch();

if (!$payment) {
    redirect('dashboard.php', 'Payment not found.', 'error');
}

// Get associated kit download
$stmt = $pdo->prepare("SELECT * FROM kit_downloads WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
$stmt->execute([$user_id]);
$kit_download = $stmt->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="<?php echo getAssetUrl('css/style.css'); ?>" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">🎉 <?php echo APP_NAME; ?></a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">Dashboard</a>
                <a class="nav-link" href="logout.php">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card border-0 shadow-lg">
                    <div class="card-body text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-check-circle fa-5x text-success"></i>
                        </div>
                        
                        <h1 class="display-5 text-success mb-4">Payment Successful!</h1>
                        
                        <p class="lead mb-4">
                            Thank you for your purchase. Your festival kit has been generated and is ready for download.
                        </p>
                        
                        <!-- Payment Details -->
                        <div class="card bg-light mb-4">
                            <div class="card-body">
                                <h5 class="card-title">Payment Details</h5>
                                <div class="row">
                                    <div class="col-sm-6">
                                        <strong>Transaction ID:</strong><br>
                                        <code><?php echo htmlspecialchars($payment['transaction_id']); ?></code>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>Amount Paid:</strong><br>
                                        <?php echo formatCurrency($payment['amount']); ?>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-sm-6">
                                        <strong>Payment Date:</strong><br>
                                        <?php echo date('M j, Y g:i A', strtotime($payment['created_at'])); ?>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>Status:</strong><br>
                                        <span class="badge bg-success">Completed</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Download Section -->
                        <?php if ($kit_download): ?>
                            <div class="mb-4">
                                <h5 class="mb-3">Your Festival Kit is Ready!</h5>
                                <a href="download.php?file=<?php echo urlencode(basename($kit_download['generated_zip_path'])); ?>&type=kit" 
                                   class="btn btn-success btn-lg me-3">
                                    <i class="fas fa-download me-2"></i>Download Festival Kit
                                </a>
                                <p class="small text-muted mt-2">
                                    ZIP file containing all festival images with your branding
                                </p>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Next Steps -->
                        <div class="card border-primary mb-4">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">What's Next?</h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-start">
                                    <div class="col-md-4 mb-3">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-download fa-2x text-primary"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6>1. Download Your Kit</h6>
                                                <p class="small mb-0">Extract the ZIP file to access all your festival images.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-share-alt fa-2x text-primary"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6>2. Share on Social Media</h6>
                                                <p class="small mb-0">Post your branded festival images on social platforms.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-star fa-2x text-primary"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6>3. Upgrade to Premium</h6>
                                                <p class="small mb-0">Get auto-posting features and more benefits.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-center gap-3">
                            <a href="dashboard.php" class="btn btn-primary">
                                <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                            </a>
                            <a href="kit_generator.php" class="btn btn-outline-primary">
                                <i class="fas fa-plus me-2"></i>Generate Another Kit
                            </a>
                        </div>
                        
                        <!-- Support Info -->
                        <div class="mt-5 pt-4 border-top">
                            <p class="text-muted mb-2">
                                <strong>Need Help?</strong>
                            </p>
                            <p class="small text-muted">
                                If you have any questions about your purchase or need assistance, 
                                please contact our support team at 
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
    
    <script>
        // Auto-hide success message after 10 seconds
        setTimeout(function() {
            $('.alert-success').fadeOut('slow');
        }, 10000);
    </script>
</body>
</html>
