<?php
// <PERSON>ript to create sample template images for FestivalKit
require_once 'config/config.php';

// Check if GD extension is loaded
if (!extension_loaded('gd')) {
    die('GD extension is required to create sample images.');
}

// Create directories if they don't exist
$dirs = [
    'assets/images',
    'uploads/templates'
];

foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Function to create a sample template image
function createSampleTemplate($filename, $title, $color1, $color2, $width = 1200, $height = 800) {
    // Create image
    $image = imagecreatetruecolor($width, $height);
    
    // Create colors
    $color1_rgb = hexToRgb($color1);
    $color2_rgb = hexToRgb($color2);
    
    // Create gradient background
    for ($y = 0; $y < $height; $y++) {
        $ratio = $y / $height;
        $r = $color1_rgb[0] * (1 - $ratio) + $color2_rgb[0] * $ratio;
        $g = $color1_rgb[1] * (1 - $ratio) + $color2_rgb[1] * $ratio;
        $b = $color1_rgb[2] * (1 - $ratio) + $color2_rgb[2] * $ratio;
        
        $color = imagecolorallocate($image, $r, $g, $b);
        imageline($image, 0, $y, $width, $y, $color);
    }
    
    // Add decorative elements
    $white = imagecolorallocate($image, 255, 255, 255);
    $overlay = imagecolorallocate($image, 255, 255, 255);
    
    // Add some decorative circles
    for ($i = 0; $i < 10; $i++) {
        $x = rand(0, $width);
        $y = rand(0, $height);
        $size = rand(20, 100);
        $alpha = rand(10, 30);
        
        $circle_color = imagecolorallocatealpha($image, 255, 255, 255, 127 - $alpha);
        imagefilledellipse($image, $x, $y, $size, $size, $circle_color);
    }
    
    // Add title text
    $font_size = 48;
    $text_color = imagecolorallocate($image, 255, 255, 255);
    
    // Calculate text position (center)
    $text_box = imagettfbbox($font_size, 0, __DIR__ . '/assets/fonts/arial.ttf', $title);
    if (!$text_box) {
        // Fallback to built-in font if TTF not available
        $text_x = ($width - strlen($title) * 20) / 2;
        $text_y = $height / 2;
        imagestring($image, 5, $text_x, $text_y, $title, $text_color);
    } else {
        $text_width = $text_box[4] - $text_box[0];
        $text_height = $text_box[1] - $text_box[7];
        $text_x = ($width - $text_width) / 2;
        $text_y = ($height + $text_height) / 2;
        imagettftext($image, $font_size, 0, $text_x, $text_y, $text_color, __DIR__ . '/assets/fonts/arial.ttf', $title);
    }
    
    // Add subtitle
    $subtitle = "Celebrate with Joy!";
    $subtitle_size = 24;
    $subtitle_y = $text_y + 80;
    $subtitle_x = ($width - strlen($subtitle) * 12) / 2;
    imagestring($image, 4, $subtitle_x, $subtitle_y, $subtitle, $text_color);
    
    // Save image
    imagejpeg($image, $filename, 90);
    imagedestroy($image);
    
    return file_exists($filename);
}

// Function to convert hex color to RGB
function hexToRgb($hex) {
    $hex = ltrim($hex, '#');
    return [
        hexdec(substr($hex, 0, 2)),
        hexdec(substr($hex, 2, 2)),
        hexdec(substr($hex, 4, 2))
    ];
}

// Create hero image
function createHeroImage($filename, $width = 1200, $height = 600) {
    $image = imagecreatetruecolor($width, $height);
    
    // Create gradient background
    for ($y = 0; $y < $height; $y++) {
        $ratio = $y / $height;
        $r = 102 * (1 - $ratio) + 118 * $ratio;
        $g = 126 * (1 - $ratio) + 75 * $ratio;
        $b = 234 * (1 - $ratio) + 162 * $ratio;
        
        $color = imagecolorallocate($image, $r, $g, $b);
        imageline($image, 0, $y, $width, $y, $color);
    }
    
    // Add decorative elements
    $white = imagecolorallocate($image, 255, 255, 255);
    
    // Add some festival-themed shapes
    for ($i = 0; $i < 20; $i++) {
        $x = rand(0, $width);
        $y = rand(0, $height);
        $size = rand(10, 50);
        $alpha = rand(20, 60);
        
        $shape_color = imagecolorallocatealpha($image, 255, 255, 255, 127 - $alpha);
        
        if ($i % 3 == 0) {
            // Circle
            imagefilledellipse($image, $x, $y, $size, $size, $shape_color);
        } elseif ($i % 3 == 1) {
            // Star-like shape
            $points = array(
                $x, $y - $size/2,
                $x + $size/4, $y - $size/4,
                $x + $size/2, $y,
                $x + $size/4, $y + $size/4,
                $x, $y + $size/2,
                $x - $size/4, $y + $size/4,
                $x - $size/2, $y,
                $x - $size/4, $y - $size/4
            );
            imagefilledpolygon($image, $points, 8, $shape_color);
        } else {
            // Rectangle
            imagefilledrectangle($image, $x - $size/2, $y - $size/2, $x + $size/2, $y + $size/2, $shape_color);
        }
    }
    
    // Add main text
    $title = "Festival Images Made Easy";
    $text_color = imagecolorallocate($image, 255, 255, 255);
    $text_x = ($width - strlen($title) * 25) / 2;
    $text_y = $height / 2 - 50;
    imagestring($image, 5, $text_x, $text_y, $title, $text_color);
    
    // Add subtitle
    $subtitle = "Professional festival marketing in minutes";
    $subtitle_x = ($width - strlen($subtitle) * 12) / 2;
    $subtitle_y = $text_y + 60;
    imagestring($image, 4, $subtitle_x, $subtitle_y, $subtitle, $text_color);
    
    // Save image
    imagejpeg($image, $filename, 90);
    imagedestroy($image);
    
    return file_exists($filename);
}

// Create sample images
$templates = [
    ['filename' => 'uploads/templates/newyear_template1.jpg', 'title' => 'Happy New Year', 'color1' => '#FFD700', 'color2' => '#FF6B6B'],
    ['filename' => 'uploads/templates/newyear_template2.jpg', 'title' => 'New Year 2024', 'color1' => '#4ECDC4', 'color2' => '#44A08D'],
    ['filename' => 'uploads/templates/valentine_template1.jpg', 'title' => 'Valentine\'s Day', 'color1' => '#FF6B9D', 'color2' => '#C44569'],
    ['filename' => 'uploads/templates/eid_template1.jpg', 'title' => 'Eid Mubarak', 'color1' => '#00C9FF', 'color2' => '#92FE9D'],
    ['filename' => 'uploads/templates/christmas_template1.jpg', 'title' => 'Merry Christmas', 'color1' => '#FF6B6B', 'color2' => '#4ECDC4'],
    ['filename' => 'uploads/templates/diwali_template1.jpg', 'title' => 'Happy Diwali', 'color1' => '#FFB347', 'color2' => '#FFCC02'],
    ['filename' => 'assets/images/sample-festival-image.jpg', 'title' => 'Sample Festival', 'color1' => '#667eea', 'color2' => '#764ba2']
];

$created_count = 0;

foreach ($templates as $template) {
    if (createSampleTemplate($template['filename'], $template['title'], $template['color1'], $template['color2'])) {
        echo "Created: " . $template['filename'] . "\n";
        $created_count++;
    } else {
        echo "Failed to create: " . $template['filename'] . "\n";
    }
}

// Create hero image
if (createHeroImage('assets/images/hero-image.jpg')) {
    echo "Created: assets/images/hero-image.jpg\n";
    $created_count++;
} else {
    echo "Failed to create: assets/images/hero-image.jpg\n";
}

echo "\nTotal images created: $created_count\n";
echo "Sample images have been generated successfully!\n";
?>
