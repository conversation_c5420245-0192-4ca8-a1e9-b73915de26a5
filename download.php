<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

// Require user login
requireLogin();

$user_id = getCurrentUserId();

// Get parameters
$filename = $_GET['file'] ?? '';
$type = $_GET['type'] ?? '';

if (empty($filename) || empty($type)) {
    die('Invalid download request.');
}

// Determine file path based on type
switch ($type) {
    case 'generated':
        $file_path = ROOT_PATH . '/' . UPLOAD_DIR_GENERATED . $filename;
        $download_name = 'festival_image_' . date('Y-m-d_H-i-s') . '.jpg';
        break;
    case 'kit':
        $file_path = ROOT_PATH . '/' . UPLOAD_DIR_GENERATED . $filename;
        $download_name = 'festival_kit_' . date('Y-m-d_H-i-s') . '.zip';
        break;
    default:
        die('Invalid download type.');
}

// Security check: ensure filename doesn't contain path traversal
if (strpos($filename, '..') !== false || strpos($filename, '/') !== false || strpos($filename, '\\') !== false) {
    die('Invalid filename.');
}

// Check if file exists
if (!file_exists($file_path)) {
    die('File not found.');
}

// Additional security: check if file belongs to user (for generated files)
if ($type === 'generated' && strpos($filename, 'generated_' . $user_id . '_') !== 0) {
    die('Access denied.');
}

// Log download if it's a kit
if ($type === 'kit') {
    try {
        $stmt = $pdo->prepare("UPDATE kit_downloads SET download_count = download_count + 1 WHERE user_id = ? AND generated_zip_path LIKE ?");
        $stmt->execute([$user_id, '%' . $filename]);
    } catch (Exception $e) {
        // Log error but continue with download
        error_log('Failed to update download count: ' . $e->getMessage());
    }
}

// Set headers for file download
header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename="' . $download_name . '"');
header('Content-Length: ' . filesize($file_path));
header('Cache-Control: no-cache, must-revalidate');
header('Expires: 0');

// Output file
readfile($file_path);
exit;
?>
