<?php
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../config/session.php';
require_once '../includes/functions.php';

// Require admin login
requireAdminLogin();

$admin_id = getCurrentAdminId();

// Handle AI design generation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['generate_design'])) {
    try {
        // Validate CSRF token
        if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid request token.');
        }
        
        $festival_name = sanitize_input($_POST['festival_name'] ?? '');
        $design_style = sanitize_input($_POST['design_style'] ?? '');
        $color_scheme = sanitize_input($_POST['color_scheme'] ?? '');
        $theme = sanitize_input($_POST['theme'] ?? '');
        
        if (empty($festival_name)) {
            throw new Exception('Festival name is required.');
        }
        
        // Generate AI design using OpenAI DALL-E API (placeholder for now)
        $ai_result = generateAIDesign($festival_name, $design_style, $color_scheme, $theme);
        
        if ($ai_result['success']) {
            setFlashMessage('AI design generated successfully!', 'success');
            $generated_image = $ai_result['image_url'];
        } else {
            throw new Exception($ai_result['message']);
        }
        
    } catch (Exception $e) {
        setFlashMessage($e->getMessage(), 'error');
    }
}

// Get existing templates for reference
$stmt = $pdo->query("SELECT t.*, f.name as festival_name FROM templates t JOIN festivals f ON t.festival_id = f.id ORDER BY f.name, t.name");
$existing_templates = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Creative Designer - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .ai-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            margin: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .ai-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            border: none;
            transition: all 0.3s ease;
        }
        
        .ai-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .ai-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            margin-bottom: 40px;
        }
        
        .ai-header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
        }
        
        .form-control, .form-select {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-ai {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-weight: 700;
            color: white;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }
        
        .btn-ai:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .design-preview {
            border: 3px dashed #dee2e6;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
        }
        
        .design-preview img {
            max-width: 100%;
            max-height: 400px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .style-option {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }
        
        .style-option:hover, .style-option.active {
            border-color: #667eea;
            background: #f8f9ff;
            transform: translateY(-2px);
        }
        
        .color-palette {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 10px;
        }
        
        .color-dot {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }
        
        .loading-spinner {
            display: none;
        }
        
        .loading-spinner.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="ai-container">
        <?php displayFlashMessage(); ?>
        
        <div class="ai-header">
            <h1><i class="fas fa-robot me-3"></i>AI Creative Designer</h1>
            <p class="mb-0">Generate stunning festival templates using artificial intelligence</p>
        </div>
        
        <div class="row">
            <div class="col-lg-6">
                <div class="ai-card">
                    <h3 class="mb-4"><i class="fas fa-magic me-2"></i>Design Generator</h3>
                    
                    <form method="POST" id="aiDesignForm">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="mb-4">
                            <label class="form-label fw-semibold">Festival Name</label>
                            <input type="text" name="festival_name" class="form-control" placeholder="e.g., Diwali, Christmas, New Year" required>
                        </div>
                        
                        <div class="mb-4">
                            <label class="form-label fw-semibold">Design Style</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="style-option" data-style="modern">
                                        <i class="fas fa-gem fa-2x text-primary mb-2"></i>
                                        <h6>Modern</h6>
                                        <small class="text-muted">Clean, minimalist design</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="style-option" data-style="traditional">
                                        <i class="fas fa-mosque fa-2x text-warning mb-2"></i>
                                        <h6>Traditional</h6>
                                        <small class="text-muted">Cultural, ornate patterns</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="style-option" data-style="elegant">
                                        <i class="fas fa-crown fa-2x text-success mb-2"></i>
                                        <h6>Elegant</h6>
                                        <small class="text-muted">Sophisticated, luxury feel</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="style-option" data-style="playful">
                                        <i class="fas fa-heart fa-2x text-danger mb-2"></i>
                                        <h6>Playful</h6>
                                        <small class="text-muted">Fun, vibrant, energetic</small>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" name="design_style" id="selectedStyle" value="">
                        </div>
                        
                        <div class="mb-4">
                            <label class="form-label fw-semibold">Color Scheme</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="style-option" data-color="warm">
                                        <h6>Warm</h6>
                                        <div class="color-palette">
                                            <div class="color-dot" style="background: #ff6b6b;"></div>
                                            <div class="color-dot" style="background: #feca57;"></div>
                                            <div class="color-dot" style="background: #ff9ff3;"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="style-option" data-color="cool">
                                        <h6>Cool</h6>
                                        <div class="color-palette">
                                            <div class="color-dot" style="background: #54a0ff;"></div>
                                            <div class="color-dot" style="background: #5f27cd;"></div>
                                            <div class="color-dot" style="background: #00d2d3;"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="style-option" data-color="gold">
                                        <h6>Golden</h6>
                                        <div class="color-palette">
                                            <div class="color-dot" style="background: #f39c12;"></div>
                                            <div class="color-dot" style="background: #d35400;"></div>
                                            <div class="color-dot" style="background: #e67e22;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" name="color_scheme" id="selectedColor" value="">
                        </div>
                        
                        <div class="mb-4">
                            <label class="form-label fw-semibold">Theme Elements</label>
                            <textarea name="theme" class="form-control" rows="3" placeholder="Describe specific elements you want (e.g., fireworks, flowers, lights, patterns)"></textarea>
                        </div>
                        
                        <button type="submit" name="generate_design" class="btn btn-ai w-100">
                            <i class="fas fa-magic me-2"></i>Generate AI Design
                        </button>
                        
                        <div class="loading-spinner text-center mt-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Generating...</span>
                            </div>
                            <p class="mt-2">AI is creating your design...</p>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="ai-card">
                    <h3 class="mb-4"><i class="fas fa-eye me-2"></i>Design Preview</h3>
                    
                    <div class="design-preview" id="designPreview">
                        <?php if (isset($generated_image)): ?>
                            <img src="<?php echo htmlspecialchars($generated_image); ?>" alt="Generated Design">
                        <?php else: ?>
                            <div class="text-center">
                                <i class="fas fa-image fa-4x text-muted mb-3"></i>
                                <h5 class="text-muted">Your AI-generated design will appear here</h5>
                                <p class="text-muted">Fill out the form and click "Generate AI Design" to create a unique festival template</p>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <?php if (isset($generated_image)): ?>
                    <div class="mt-4 text-center">
                        <button class="btn btn-success me-2">
                            <i class="fas fa-save me-2"></i>Save as Template
                        </button>
                        <button class="btn btn-outline-primary">
                            <i class="fas fa-download me-2"></i>Download
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="ai-card">
                    <h3 class="mb-4"><i class="fas fa-history me-2"></i>Recent AI Generations</h3>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center p-3">
                                    <div class="bg-light rounded mb-2" style="height: 120px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-image fa-2x text-muted"></i>
                                    </div>
                                    <h6 class="mb-1">Diwali Modern</h6>
                                    <small class="text-muted">2 hours ago</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center p-3">
                                    <div class="bg-light rounded mb-2" style="height: 120px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-image fa-2x text-muted"></i>
                                    </div>
                                    <h6 class="mb-1">Christmas Elegant</h6>
                                    <small class="text-muted">1 day ago</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center p-3">
                                    <div class="bg-light rounded mb-2" style="height: 120px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-image fa-2x text-muted"></i>
                                    </div>
                                    <h6 class="mb-1">New Year Playful</h6>
                                    <small class="text-muted">3 days ago</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body text-center p-3">
                                    <div class="bg-light rounded mb-2" style="height: 120px; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-plus fa-2x text-primary"></i>
                                    </div>
                                    <h6 class="mb-1">Generate More</h6>
                                    <small class="text-muted">Create new design</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Admin Dashboard
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Style selection
            $('.style-option').click(function() {
                const type = $(this).data('style') || $(this).data('color');
                const parent = $(this).parent().parent();
                
                parent.find('.style-option').removeClass('active');
                $(this).addClass('active');
                
                if ($(this).data('style')) {
                    $('#selectedStyle').val($(this).data('style'));
                }
                if ($(this).data('color')) {
                    $('#selectedColor').val($(this).data('color'));
                }
            });
            
            // Form submission
            $('#aiDesignForm').submit(function() {
                $('.loading-spinner').addClass('show');
                $('.btn-ai').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Generating...');
            });
        });
    </script>
</body>
</html>
