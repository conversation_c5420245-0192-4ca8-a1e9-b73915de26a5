<?php
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../config/session.php';
require_once '../includes/functions.php';

// Require admin login
requireAdminLogin();

$action = $_GET['action'] ?? 'list';
$user_id = $_GET['id'] ?? null;

// Handle user package update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $action === 'update_package') {
    if (validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $user_id = intval($_POST['user_id']);
        $package_type = $_POST['package_type'];
        
        if (in_array($package_type, ['free', 'paid', 'premium'])) {
            $stmt = $pdo->prepare("UPDATE users SET package_type = ? WHERE id = ?");
            if ($stmt->execute([$package_type, $user_id])) {
                setFlashMessage('User package updated successfully!', 'success');
            } else {
                setFlashMessage('Failed to update user package.', 'error');
            }
        }
    }
    redirect('users.php');
}

// Get user statistics
$stats = [];
$stmt = $pdo->query("SELECT package_type, COUNT(*) as count FROM users GROUP BY package_type");
while ($row = $stmt->fetch()) {
    $stats[$row['package_type']] = $row['count'];
}

// Get users with additional data
$users = [];
if ($action === 'list') {
    $stmt = $pdo->query("
        SELECT u.*, 
               COUNT(DISTINCT p.id) as total_payments,
               SUM(CASE WHEN p.status = 'completed' THEN p.amount ELSE 0 END) as total_spent,
               COUNT(DISTINCT kd.id) as total_downloads,
               MAX(p.created_at) as last_payment
        FROM users u
        LEFT JOIN payments p ON u.id = p.user_id
        LEFT JOIN kit_downloads kd ON u.id = kd.user_id
        GROUP BY u.id
        ORDER BY u.created_at DESC
    ");
    $users = $stmt->fetchAll();
}

// Get user details for view
$user_details = null;
if ($action === 'view' && $user_id) {
    // Get user info
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user_details = $stmt->fetch();
    
    if ($user_details) {
        // Get user uploads
        $stmt = $pdo->prepare("SELECT * FROM user_uploads WHERE user_id = ? ORDER BY created_at DESC");
        $stmt->execute([$user_id]);
        $user_details['uploads'] = $stmt->fetchAll();
        
        // Get user payments
        $stmt = $pdo->prepare("SELECT * FROM payments WHERE user_id = ? ORDER BY created_at DESC");
        $stmt->execute([$user_id]);
        $user_details['payments'] = $stmt->fetchAll();
        
        // Get user downloads
        $stmt = $pdo->prepare("SELECT * FROM kit_downloads WHERE user_id = ? ORDER BY created_at DESC");
        $stmt->execute([$user_id]);
        $user_details['downloads'] = $stmt->fetchAll();
        
        // Get scheduled posts
        $stmt = $pdo->prepare("
            SELECT sp.*, f.name as festival_name 
            FROM scheduled_posts sp 
            JOIN festivals f ON sp.festival_id = f.id 
            WHERE sp.user_id = ? 
            ORDER BY sp.post_date DESC
        ");
        $stmt->execute([$user_id]);
        $user_details['scheduled_posts'] = $stmt->fetchAll();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="<?php echo getAssetUrl('css/style.css'); ?>" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 admin-sidebar">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">🎉 <?php echo APP_NAME; ?></h5>
                        <small class="text-muted">Admin Panel</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="festivals.php">
                                <i class="fas fa-calendar-alt me-2"></i>Festivals
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="templates.php">
                                <i class="fas fa-images me-2"></i>Templates
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="users.php">
                                <i class="fas fa-users me-2"></i>Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="payments.php">
                                <i class="fas fa-credit-card me-2"></i>Payments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="fas fa-cog me-2"></i>Settings
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 admin-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-users me-2"></i>User Management
                    </h1>
                    <?php if ($action === 'view'): ?>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <a href="users.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Users
                            </a>
                        </div>
                    <?php endif; ?>
                </div>

                <?php displayFlashMessage(); ?>

                <?php if ($action === 'list'): ?>
                    <!-- User Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h4><?php echo $stats['free'] ?? 0; ?></h4>
                                <p><i class="fas fa-user me-2"></i>Free Users</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h4><?php echo $stats['paid'] ?? 0; ?></h4>
                                <p><i class="fas fa-user-plus me-2"></i>Paid Users</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h4><?php echo $stats['premium'] ?? 0; ?></h4>
                                <p><i class="fas fa-crown me-2"></i>Premium Users</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h4><?php echo array_sum($stats); ?></h4>
                                <p><i class="fas fa-users me-2"></i>Total Users</p>
                            </div>
                        </div>
                    </div>

                    <!-- Users Table -->
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover data-table">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Email</th>
                                            <th>Package</th>
                                            <th>Total Spent</th>
                                            <th>Downloads</th>
                                            <th>Joined</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($users as $user): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-circle me-3">
                                                            <?php echo strtoupper(substr($user['name'], 0, 2)); ?>
                                                        </div>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($user['name']); ?></strong>
                                                            <?php if ($user['last_payment']): ?>
                                                                <br><small class="text-muted">
                                                                    Last active: <?php echo date('M j', strtotime($user['last_payment'])); ?>
                                                                </small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                                <td>
                                                    <form method="POST" action="users.php?action=update_package" class="d-inline">
                                                        <?php echo getCSRFTokenField(); ?>
                                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                        <select name="package_type" class="form-select form-select-sm" 
                                                                onchange="this.form.submit()" style="width: auto;">
                                                            <option value="free" <?php echo $user['package_type'] === 'free' ? 'selected' : ''; ?>>Free</option>
                                                            <option value="paid" <?php echo $user['package_type'] === 'paid' ? 'selected' : ''; ?>>Paid</option>
                                                            <option value="premium" <?php echo $user['package_type'] === 'premium' ? 'selected' : ''; ?>>Premium</option>
                                                        </select>
                                                    </form>
                                                </td>
                                                <td><?php echo formatCurrency($user['total_spent'] ?? 0); ?></td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo $user['total_downloads']; ?></span>
                                                </td>
                                                <td><?php echo date('M j, Y', strtotime($user['created_at'])); ?></td>
                                                <td>
                                                    <a href="users.php?action=view&id=<?php echo $user['id']; ?>" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                <?php elseif ($action === 'view' && $user_details): ?>
                    <!-- User Details View -->
                    <div class="row">
                        <div class="col-lg-4">
                            <!-- User Info Card -->
                            <div class="card border-0 shadow-sm mb-4">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-user me-2"></i>User Information
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="text-center mb-3">
                                        <div class="avatar-circle mx-auto mb-3" style="width: 80px; height: 80px; font-size: 2rem;">
                                            <?php echo strtoupper(substr($user_details['name'], 0, 2)); ?>
                                        </div>
                                        <h5><?php echo htmlspecialchars($user_details['name']); ?></h5>
                                        <p class="text-muted"><?php echo htmlspecialchars($user_details['email']); ?></p>
                                    </div>
                                    
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <h6 class="text-primary"><?php echo ucfirst($user_details['package_type']); ?></h6>
                                            <small class="text-muted">Package</small>
                                        </div>
                                        <div class="col-6">
                                            <h6 class="text-success"><?php echo date('M Y', strtotime($user_details['created_at'])); ?></h6>
                                            <small class="text-muted">Member Since</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Stats -->
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-chart-bar me-2"></i>Quick Stats
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Total Payments:</span>
                                        <strong><?php echo count($user_details['payments']); ?></strong>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Total Downloads:</span>
                                        <strong><?php echo count($user_details['downloads']); ?></strong>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Scheduled Posts:</span>
                                        <strong><?php echo count($user_details['scheduled_posts']); ?></strong>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>Total Spent:</span>
                                        <strong class="text-success">
                                            <?php 
                                            $total_spent = 0;
                                            foreach ($user_details['payments'] as $payment) {
                                                if ($payment['status'] === 'completed') {
                                                    $total_spent += $payment['amount'];
                                                }
                                            }
                                            echo formatCurrency($total_spent);
                                            ?>
                                        </strong>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-8">
                            <!-- Tabs for different sections -->
                            <ul class="nav nav-tabs" id="userTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="payments-tab" data-bs-toggle="tab" 
                                            data-bs-target="#payments" type="button" role="tab">
                                        <i class="fas fa-credit-card me-2"></i>Payments
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="downloads-tab" data-bs-toggle="tab" 
                                            data-bs-target="#downloads" type="button" role="tab">
                                        <i class="fas fa-download me-2"></i>Downloads
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="uploads-tab" data-bs-toggle="tab" 
                                            data-bs-target="#uploads" type="button" role="tab">
                                        <i class="fas fa-upload me-2"></i>Uploads
                                    </button>
                                </li>
                            </ul>

                            <div class="tab-content" id="userTabsContent">
                                <!-- Payments Tab -->
                                <div class="tab-pane fade show active" id="payments" role="tabpanel">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body">
                                            <?php if (empty($user_details['payments'])): ?>
                                                <p class="text-muted text-center py-3">No payments found</p>
                                            <?php else: ?>
                                                <div class="table-responsive">
                                                    <table class="table table-sm">
                                                        <thead>
                                                            <tr>
                                                                <th>Date</th>
                                                                <th>Amount</th>
                                                                <th>Status</th>
                                                                <th>Transaction ID</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <?php foreach ($user_details['payments'] as $payment): ?>
                                                                <tr>
                                                                    <td><?php echo date('M j, Y g:i A', strtotime($payment['created_at'])); ?></td>
                                                                    <td><?php echo formatCurrency($payment['amount']); ?></td>
                                                                    <td>
                                                                        <span class="badge bg-<?php echo $payment['status'] === 'completed' ? 'success' : 'warning'; ?>">
                                                                            <?php echo ucfirst($payment['status']); ?>
                                                                        </span>
                                                                    </td>
                                                                    <td>
                                                                        <code><?php echo htmlspecialchars($payment['transaction_id'] ?? 'N/A'); ?></code>
                                                                    </td>
                                                                </tr>
                                                            <?php endforeach; ?>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Downloads Tab -->
                                <div class="tab-pane fade" id="downloads" role="tabpanel">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body">
                                            <?php if (empty($user_details['downloads'])): ?>
                                                <p class="text-muted text-center py-3">No downloads found</p>
                                            <?php else: ?>
                                                <div class="list-group list-group-flush">
                                                    <?php foreach ($user_details['downloads'] as $download): ?>
                                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                                            <div>
                                                                <h6 class="mb-1">Festival Kit</h6>
                                                                <small class="text-muted">
                                                                    <?php echo date('M j, Y g:i A', strtotime($download['created_at'])); ?>
                                                                </small>
                                                            </div>
                                                            <span class="badge bg-primary"><?php echo $download['download_count']; ?> downloads</span>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Uploads Tab -->
                                <div class="tab-pane fade" id="uploads" role="tabpanel">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-body">
                                            <?php if (empty($user_details['uploads'])): ?>
                                                <p class="text-muted text-center py-3">No uploads found</p>
                                            <?php else: ?>
                                                <?php foreach ($user_details['uploads'] as $upload): ?>
                                                    <div class="row align-items-center mb-3 p-3 border rounded">
                                                        <div class="col-md-2">
                                                            <?php if ($upload['logo_path']): ?>
                                                                <img src="<?php echo getUploadUrl($upload['logo_path']); ?>" 
                                                                     alt="Logo" class="img-fluid rounded" style="max-height: 60px;">
                                                            <?php else: ?>
                                                                <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                                                     style="height: 60px;">
                                                                    <i class="fas fa-image text-muted"></i>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="col-md-10">
                                                            <h6 class="mb-1"><?php echo htmlspecialchars($upload['tagline'] ?: 'No tagline'); ?></h6>
                                                            <p class="mb-1 small"><?php echo htmlspecialchars($upload['contact_info'] ?: 'No contact info'); ?></p>
                                                            <small class="text-muted">
                                                                Uploaded: <?php echo date('M j, Y', strtotime($upload['created_at'])); ?>
                                                            </small>
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
    
    <style>
        .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
    </style>
</body>
</html>
