<?php
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../config/session.php';
require_once '../includes/functions.php';

// Require admin login
requireAdminLogin();

$action = $_GET['action'] ?? 'list';

// Handle CSV export
if ($action === 'export') {
    $format = $_GET['format'] ?? 'csv';
    
    if ($format === 'csv') {
        // Get all payments with user info
        $stmt = $pdo->query("
            SELECT p.*, u.name as user_name, u.email as user_email
            FROM payments p
            JOIN users u ON p.user_id = u.id
            ORDER BY p.created_at DESC
        ");
        $payments = $stmt->fetchAll();
        
        // Set headers for CSV download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="payments_' . date('Y-m-d') . '.csv"');
        
        // Create CSV output
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, [
            'Payment ID',
            'User Name',
            'User Email',
            'Amount',
            'Status',
            'Payment Method',
            'Transaction ID',
            'Date'
        ]);
        
        // CSV data
        foreach ($payments as $payment) {
            fputcsv($output, [
                $payment['id'],
                $payment['user_name'],
                $payment['user_email'],
                $payment['amount'],
                $payment['status'],
                $payment['payment_method'],
                $payment['transaction_id'],
                $payment['created_at']
            ]);
        }
        
        fclose($output);
        exit;
    }
}

// Get payment statistics
$stats = [];

// Total revenue
$stmt = $pdo->query("SELECT SUM(amount) as total FROM payments WHERE status = 'completed'");
$stats['total_revenue'] = $stmt->fetch()['total'] ?? 0;

// Revenue this month
$stmt = $pdo->query("SELECT SUM(amount) as total FROM payments WHERE status = 'completed' AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())");
$stats['revenue_month'] = $stmt->fetch()['total'] ?? 0;

// Total transactions
$stmt = $pdo->query("SELECT COUNT(*) as total FROM payments");
$stats['total_transactions'] = $stmt->fetch()['total'];

// Successful transactions
$stmt = $pdo->query("SELECT COUNT(*) as total FROM payments WHERE status = 'completed'");
$stats['successful_transactions'] = $stmt->fetch()['total'];

// Failed transactions
$stmt = $pdo->query("SELECT COUNT(*) as total FROM payments WHERE status = 'failed'");
$stats['failed_transactions'] = $stmt->fetch()['total'];

// Pending transactions
$stmt = $pdo->query("SELECT COUNT(*) as total FROM payments WHERE status = 'pending'");
$stats['pending_transactions'] = $stmt->fetch()['total'];

// Get payments with user info
$payments = [];
$filter_status = $_GET['status'] ?? '';
$filter_method = $_GET['method'] ?? '';

$sql = "SELECT p.*, u.name as user_name, u.email as user_email
        FROM payments p
        JOIN users u ON p.user_id = u.id";
$params = [];
$conditions = [];

if ($filter_status) {
    $conditions[] = "p.status = ?";
    $params[] = $filter_status;
}

if ($filter_method) {
    $conditions[] = "p.payment_method = ?";
    $params[] = $filter_method;
}

if (!empty($conditions)) {
    $sql .= " WHERE " . implode(" AND ", $conditions);
}

$sql .= " ORDER BY p.created_at DESC LIMIT 1000";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$payments = $stmt->fetchAll();

// Get payment methods for filter
$stmt = $pdo->query("SELECT DISTINCT payment_method FROM payments WHERE payment_method IS NOT NULL");
$payment_methods = $stmt->fetchAll(PDO::FETCH_COLUMN);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Management - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="<?php echo getAssetUrl('css/style.css'); ?>" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 admin-sidebar">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">🎉 <?php echo APP_NAME; ?></h5>
                        <small class="text-muted">Admin Panel</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="festivals.php">
                                <i class="fas fa-calendar-alt me-2"></i>Festivals
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="templates.php">
                                <i class="fas fa-images me-2"></i>Templates
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="fas fa-users me-2"></i>Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="payments.php">
                                <i class="fas fa-credit-card me-2"></i>Payments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="fas fa-cog me-2"></i>Settings
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 admin-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-credit-card me-2"></i>Payment Management
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-download me-1"></i>Export
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="payments.php?action=export&format=csv">
                                    <i class="fas fa-file-csv me-2"></i>Export as CSV
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <?php displayFlashMessage(); ?>

                <!-- Payment Statistics -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <h4><?php echo formatCurrency($stats['total_revenue']); ?></h4>
                            <p><i class="fas fa-dollar-sign me-2"></i>Total Revenue</p>
                            <small class="text-light"><?php echo formatCurrency($stats['revenue_month']); ?> this month</small>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <h4><?php echo number_format($stats['successful_transactions']); ?></h4>
                            <p><i class="fas fa-check-circle me-2"></i>Successful</p>
                            <small class="text-light">Completed payments</small>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <h4><?php echo number_format($stats['failed_transactions']); ?></h4>
                            <p><i class="fas fa-times-circle me-2"></i>Failed</p>
                            <small class="text-light">Failed payments</small>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <h4><?php echo number_format($stats['pending_transactions']); ?></h4>
                            <p><i class="fas fa-clock me-2"></i>Pending</p>
                            <small class="text-light">Awaiting processing</small>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">All Statuses</option>
                                    <option value="completed" <?php echo $filter_status === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                    <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="failed" <?php echo $filter_status === 'failed' ? 'selected' : ''; ?>>Failed</option>
                                    <option value="refunded" <?php echo $filter_status === 'refunded' ? 'selected' : ''; ?>>Refunded</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="method" class="form-label">Payment Method</label>
                                <select class="form-select" id="method" name="method">
                                    <option value="">All Methods</option>
                                    <?php foreach ($payment_methods as $method): ?>
                                        <option value="<?php echo htmlspecialchars($method); ?>" 
                                                <?php echo $filter_method === $method ? 'selected' : ''; ?>>
                                            <?php echo ucfirst(htmlspecialchars($method)); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter me-2"></i>Filter
                                    </button>
                                    <a href="payments.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>Clear
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Payments Table -->
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <?php if (empty($payments)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No payments found</h5>
                                <p class="text-muted">Payments will appear here once users make purchases.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover data-table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>User</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Method</th>
                                            <th>Transaction ID</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($payments as $payment): ?>
                                            <tr>
                                                <td>
                                                    <code>#<?php echo $payment['id']; ?></code>
                                                </td>
                                                <td>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($payment['user_name']); ?></strong>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($payment['user_email']); ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <strong><?php echo formatCurrency($payment['amount']); ?></strong>
                                                </td>
                                                <td>
                                                    <?php
                                                    $status_class = 'secondary';
                                                    switch ($payment['status']) {
                                                        case 'completed':
                                                            $status_class = 'success';
                                                            break;
                                                        case 'failed':
                                                            $status_class = 'danger';
                                                            break;
                                                        case 'pending':
                                                            $status_class = 'warning';
                                                            break;
                                                        case 'refunded':
                                                            $status_class = 'info';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge bg-<?php echo $status_class; ?>">
                                                        <?php echo ucfirst($payment['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($payment['payment_method']): ?>
                                                        <span class="badge bg-light text-dark">
                                                            <?php echo ucfirst(htmlspecialchars($payment['payment_method'])); ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-muted">N/A</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($payment['transaction_id']): ?>
                                                        <code class="small"><?php echo htmlspecialchars($payment['transaction_id']); ?></code>
                                                    <?php else: ?>
                                                        <span class="text-muted">N/A</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php echo date('M j, Y', strtotime($payment['created_at'])); ?>
                                                    <br><small class="text-muted"><?php echo date('g:i A', strtotime($payment['created_at'])); ?></small>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>
