<?php
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../config/session.php';
require_once '../includes/functions.php';

// Require admin login
requireAdminLogin();

$admin_id = getCurrentAdminId();

// Get dashboard statistics
$stats = [];

// Total users
$stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
$stats['total_users'] = $stmt->fetch()['total'];

// New users this month
$stmt = $pdo->query("SELECT COUNT(*) as total FROM users WHERE MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())");
$stats['new_users_month'] = $stmt->fetch()['total'];

// Total downloads
$stmt = $pdo->query("SELECT COUNT(*) as total FROM kit_downloads");
$stats['total_downloads'] = $stmt->fetch()['total'];

// Total revenue
$stmt = $pdo->query("SELECT SUM(amount) as total FROM payments WHERE status = 'completed'");
$stats['total_revenue'] = $stmt->fetch()['total'] ?? 0;

// Revenue this month
$stmt = $pdo->query("SELECT SUM(amount) as total FROM payments WHERE status = 'completed' AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())");
$stats['revenue_month'] = $stmt->fetch()['total'] ?? 0;

// Total festivals
$stmt = $pdo->query("SELECT COUNT(*) as total FROM festivals");
$stats['total_festivals'] = $stmt->fetch()['total'];

// Total templates
$stmt = $pdo->query("SELECT COUNT(*) as total FROM templates WHERE is_active = 1");
$stats['total_templates'] = $stmt->fetch()['total'];

// Recent activities
$stmt = $pdo->query("
    SELECT 'user_registration' as type, name as title, created_at, id 
    FROM users 
    UNION ALL
    SELECT 'payment' as type, CONCAT('Payment of ', amount) as title, created_at, id 
    FROM payments WHERE status = 'completed'
    UNION ALL
    SELECT 'download' as type, 'Kit Downloaded' as title, created_at, id 
    FROM kit_downloads
    ORDER BY created_at DESC 
    LIMIT 10
");
$recent_activities = $stmt->fetchAll();

// Get popular festivals (most templates)
$stmt = $pdo->query("
    SELECT f.name, COUNT(t.id) as template_count 
    FROM festivals f 
    LEFT JOIN templates t ON f.id = t.festival_id AND t.is_active = 1
    GROUP BY f.id, f.name 
    ORDER BY template_count DESC 
    LIMIT 5
");
$popular_festivals = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="<?php echo getAssetUrl('css/style.css'); ?>" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 admin-sidebar">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">🎉 <?php echo APP_NAME; ?></h5>
                        <small class="text-muted">Admin Panel</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="festivals.php">
                                <i class="fas fa-calendar-alt me-2"></i>Festivals
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="templates.php">
                                <i class="fas fa-images me-2"></i>Templates
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="ai_designer.php">
                                <i class="fas fa-robot me-2"></i>AI Designer
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="fas fa-users me-2"></i>Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="payments.php">
                                <i class="fas fa-credit-card me-2"></i>Payments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="fas fa-cog me-2"></i>Settings
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link" href="../index.php" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>View Site
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 admin-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Dashboard</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-download me-1"></i>Export
                            </button>
                        </div>
                    </div>
                </div>

                <?php displayFlashMessage(); ?>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <h4><?php echo number_format($stats['total_users']); ?></h4>
                            <p><i class="fas fa-users me-2"></i>Total Users</p>
                            <small class="text-light">+<?php echo $stats['new_users_month']; ?> this month</small>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <h4><?php echo formatCurrency($stats['total_revenue']); ?></h4>
                            <p><i class="fas fa-dollar-sign me-2"></i>Total Revenue</p>
                            <small class="text-light"><?php echo formatCurrency($stats['revenue_month']); ?> this month</small>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <h4><?php echo number_format($stats['total_downloads']); ?></h4>
                            <p><i class="fas fa-download me-2"></i>Total Downloads</p>
                            <small class="text-light">Kit downloads</small>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <h4><?php echo $stats['total_festivals']; ?></h4>
                            <p><i class="fas fa-calendar me-2"></i>Active Festivals</p>
                            <small class="text-light"><?php echo $stats['total_templates']; ?> templates</small>
                        </div>
                    </div>
                </div>

                <!-- Charts and Recent Activity -->
                <div class="row">
                    <!-- Recent Activity -->
                    <div class="col-lg-8">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-clock me-2"></i>Recent Activity
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_activities)): ?>
                                    <p class="text-muted text-center py-3">No recent activity</p>
                                <?php else: ?>
                                    <div class="list-group list-group-flush">
                                        <?php foreach ($recent_activities as $activity): ?>
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <div>
                                                    <?php
                                                    $icon = 'fas fa-info-circle';
                                                    $color = 'text-info';
                                                    switch ($activity['type']) {
                                                        case 'user_registration':
                                                            $icon = 'fas fa-user-plus';
                                                            $color = 'text-success';
                                                            break;
                                                        case 'payment':
                                                            $icon = 'fas fa-credit-card';
                                                            $color = 'text-primary';
                                                            break;
                                                        case 'download':
                                                            $icon = 'fas fa-download';
                                                            $color = 'text-warning';
                                                            break;
                                                    }
                                                    ?>
                                                    <i class="<?php echo $icon; ?> <?php echo $color; ?> me-2"></i>
                                                    <?php echo htmlspecialchars($activity['title']); ?>
                                                </div>
                                                <small class="text-muted">
                                                    <?php echo date('M j, g:i A', strtotime($activity['created_at'])); ?>
                                                </small>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Popular Festivals -->
                    <div class="col-lg-4">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-star me-2"></i>Popular Festivals
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($popular_festivals)): ?>
                                    <p class="text-muted text-center py-3">No festivals yet</p>
                                <?php else: ?>
                                    <?php foreach ($popular_festivals as $festival): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <h6 class="mb-0"><?php echo htmlspecialchars($festival['name']); ?></h6>
                                            </div>
                                            <span class="badge bg-primary">
                                                <?php echo $festival['template_count']; ?> templates
                                            </span>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="card border-0 shadow-sm mt-4">
                            <div class="card-header bg-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-bolt me-2"></i>Quick Actions
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="festivals.php?action=add" class="btn btn-primary btn-sm">
                                        <i class="fas fa-plus me-2"></i>Add Festival
                                    </a>
                                    <a href="templates.php?action=add" class="btn btn-success btn-sm">
                                        <i class="fas fa-upload me-2"></i>Upload Template
                                    </a>
                                    <a href="users.php" class="btn btn-info btn-sm">
                                        <i class="fas fa-users me-2"></i>Manage Users
                                    </a>
                                    <a href="settings.php" class="btn btn-secondary btn-sm">
                                        <i class="fas fa-cog me-2"></i>Settings
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>
