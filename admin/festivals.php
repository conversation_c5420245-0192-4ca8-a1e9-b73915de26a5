<?php
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../config/session.php';
require_once '../includes/functions.php';

// Require admin login
requireAdminLogin();

$action = $_GET['action'] ?? 'list';
$festival_id = $_GET['id'] ?? null;
$errors = [];
$success = false;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid request token.';
    } else {
        $name = sanitize_input($_POST['name'] ?? '');
        $date = $_POST['date'] ?? '';
        $description = sanitize_input($_POST['description'] ?? '');
        $is_featured = isset($_POST['is_featured']) ? 1 : 0;
        
        // Validation
        if (empty($name)) {
            $errors[] = 'Festival name is required.';
        }
        if (empty($date)) {
            $errors[] = 'Festival date is required.';
        }
        if (empty($description)) {
            $errors[] = 'Festival description is required.';
        }
        
        if (empty($errors)) {
            if ($action === 'add') {
                $stmt = $pdo->prepare("INSERT INTO festivals (name, date, description, is_featured) VALUES (?, ?, ?, ?)");
                if ($stmt->execute([$name, $date, $description, $is_featured])) {
                    $success = true;
                    setFlashMessage('Festival added successfully!', 'success');
                    redirect('festivals.php');
                } else {
                    $errors[] = 'Failed to add festival.';
                }
            } elseif ($action === 'edit' && $festival_id) {
                $stmt = $pdo->prepare("UPDATE festivals SET name = ?, date = ?, description = ?, is_featured = ? WHERE id = ?");
                if ($stmt->execute([$name, $date, $description, $is_featured, $festival_id])) {
                    $success = true;
                    setFlashMessage('Festival updated successfully!', 'success');
                    redirect('festivals.php');
                } else {
                    $errors[] = 'Failed to update festival.';
                }
            }
        }
    }
}

// Handle delete action
if ($action === 'delete' && $festival_id) {
    if (validateCSRFToken($_GET['token'] ?? '')) {
        $stmt = $pdo->prepare("DELETE FROM festivals WHERE id = ?");
        if ($stmt->execute([$festival_id])) {
            setFlashMessage('Festival deleted successfully!', 'success');
        } else {
            setFlashMessage('Failed to delete festival.', 'error');
        }
    }
    redirect('festivals.php');
}

// Get festival data for editing
$festival = null;
if ($action === 'edit' && $festival_id) {
    $stmt = $pdo->prepare("SELECT * FROM festivals WHERE id = ?");
    $stmt->execute([$festival_id]);
    $festival = $stmt->fetch();
    if (!$festival) {
        redirect('festivals.php');
    }
}

// Get all festivals for listing
$festivals = [];
if ($action === 'list') {
    $stmt = $pdo->query("SELECT f.*, COUNT(t.id) as template_count 
                        FROM festivals f 
                        LEFT JOIN templates t ON f.id = t.festival_id AND t.is_active = 1
                        GROUP BY f.id 
                        ORDER BY f.date ASC");
    $festivals = $stmt->fetchAll();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Festival Management - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="<?php echo getAssetUrl('css/style.css'); ?>" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 admin-sidebar">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">🎉 <?php echo APP_NAME; ?></h5>
                        <small class="text-muted">Admin Panel</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="festivals.php">
                                <i class="fas fa-calendar-alt me-2"></i>Festivals
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="templates.php">
                                <i class="fas fa-images me-2"></i>Templates
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="fas fa-users me-2"></i>Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="payments.php">
                                <i class="fas fa-credit-card me-2"></i>Payments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="fas fa-cog me-2"></i>Settings
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 admin-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-calendar-alt me-2"></i>Festival Management
                    </h1>
                    <?php if ($action === 'list'): ?>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <a href="festivals.php?action=add" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add Festival
                            </a>
                        </div>
                    <?php endif; ?>
                </div>

                <?php displayFlashMessage(); ?>

                <?php if ($action === 'list'): ?>
                    <!-- Festival List -->
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <?php if (empty($festivals)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No festivals found</h5>
                                    <p class="text-muted">Start by adding your first festival.</p>
                                    <a href="festivals.php?action=add" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>Add Festival
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover data-table">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Date</th>
                                                <th>Description</th>
                                                <th>Templates</th>
                                                <th>Featured</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($festivals as $fest): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($fest['name']); ?></strong>
                                                    </td>
                                                    <td>
                                                        <?php echo date('M j, Y', strtotime($fest['date'])); ?>
                                                        <?php if (strtotime($fest['date']) < time()): ?>
                                                            <small class="text-muted">(Past)</small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php echo htmlspecialchars(substr($fest['description'], 0, 100)); ?>
                                                        <?php if (strlen($fest['description']) > 100): ?>...<?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info"><?php echo $fest['template_count']; ?></span>
                                                        <a href="templates.php?festival=<?php echo $fest['id']; ?>" class="btn btn-sm btn-outline-primary ms-2">
                                                            Manage
                                                        </a>
                                                    </td>
                                                    <td>
                                                        <?php if ($fest['is_featured']): ?>
                                                            <span class="badge bg-success">Featured</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-secondary">Regular</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="festivals.php?action=edit&id=<?php echo $fest['id']; ?>" 
                                                               class="btn btn-sm btn-outline-primary">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="festivals.php?action=delete&id=<?php echo $fest['id']; ?>&token=<?php echo generateCSRFToken(); ?>" 
                                                               class="btn btn-sm btn-outline-danger delete-btn">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                <?php elseif ($action === 'add' || $action === 'edit'): ?>
                    <!-- Add/Edit Festival Form -->
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-white">
                                    <h5 class="mb-0">
                                        <?php echo $action === 'add' ? 'Add New Festival' : 'Edit Festival'; ?>
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($errors)): ?>
                                        <div class="alert alert-danger">
                                            <ul class="mb-0">
                                                <?php foreach ($errors as $error): ?>
                                                    <li><?php echo htmlspecialchars($error); ?></li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    <?php endif; ?>

                                    <form method="POST">
                                        <?php echo getCSRFTokenField(); ?>
                                        
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Festival Name *</label>
                                            <input type="text" class="form-control" id="name" name="name" 
                                                   value="<?php echo htmlspecialchars($festival['name'] ?? $_POST['name'] ?? ''); ?>" 
                                                   required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="date" class="form-label">Festival Date *</label>
                                            <input type="date" class="form-control" id="date" name="date" 
                                                   value="<?php echo $festival['date'] ?? $_POST['date'] ?? ''; ?>" 
                                                   required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="description" class="form-label">Description *</label>
                                            <textarea class="form-control" id="description" name="description" rows="4" 
                                                      required><?php echo htmlspecialchars($festival['description'] ?? $_POST['description'] ?? ''); ?></textarea>
                                        </div>
                                        
                                        <div class="mb-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" 
                                                       <?php echo ($festival['is_featured'] ?? $_POST['is_featured'] ?? false) ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="is_featured">
                                                    Feature this festival on homepage
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>
                                                <?php echo $action === 'add' ? 'Add Festival' : 'Update Festival'; ?>
                                            </button>
                                            <a href="festivals.php" class="btn btn-secondary">
                                                <i class="fas fa-times me-2"></i>Cancel
                                            </a>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>Tips
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled mb-0">
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success me-2"></i>
                                            Use clear, descriptive festival names
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success me-2"></i>
                                            Set accurate dates for proper scheduling
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success me-2"></i>
                                            Featured festivals appear on homepage
                                        </li>
                                        <li class="mb-0">
                                            <i class="fas fa-check text-success me-2"></i>
                                            Add templates after creating festival
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
</body>
</html>
