<?php
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../config/session.php';
require_once '../includes/functions.php';

// Require admin login
requireAdminLogin();

$action = $_GET['action'] ?? 'list';
$template_id = $_GET['id'] ?? null;
$festival_filter = $_GET['festival'] ?? null;
$errors = [];
$success = false;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid request token.';
    } else {
        $festival_id = intval($_POST['festival_id'] ?? 0);
        $name = sanitize_input($_POST['name'] ?? '');
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        
        // Validation
        if (!$festival_id) {
            $errors[] = 'Please select a festival.';
        }
        if (empty($name)) {
            $errors[] = 'Template name is required.';
        }
        
        // Handle file upload for new templates
        $image_path = null;
        if ($action === 'add') {
            if (!isset($_FILES['template_image']) || $_FILES['template_image']['error'] !== UPLOAD_ERR_OK) {
                $errors[] = 'Please upload a template image.';
            } else {
                $upload_result = uploadFile($_FILES['template_image'], ROOT_PATH . '/' . UPLOAD_DIR_TEMPLATES);
                if (!$upload_result['success']) {
                    $errors[] = $upload_result['message'];
                } else {
                    $image_path = UPLOAD_DIR_TEMPLATES . $upload_result['filename'];
                }
            }
        }
        
        if (empty($errors)) {
            if ($action === 'add') {
                $stmt = $pdo->prepare("INSERT INTO templates (festival_id, image_path, name, is_active) VALUES (?, ?, ?, ?)");
                if ($stmt->execute([$festival_id, $image_path, $name, $is_active])) {
                    setFlashMessage('Template added successfully!', 'success');
                    redirect('templates.php');
                } else {
                    $errors[] = 'Failed to add template.';
                }
            } elseif ($action === 'edit' && $template_id) {
                // Handle optional image update
                if (isset($_FILES['template_image']) && $_FILES['template_image']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = uploadFile($_FILES['template_image'], ROOT_PATH . '/' . UPLOAD_DIR_TEMPLATES);
                    if ($upload_result['success']) {
                        $image_path = UPLOAD_DIR_TEMPLATES . $upload_result['filename'];
                        // Delete old image
                        $stmt = $pdo->prepare("SELECT image_path FROM templates WHERE id = ?");
                        $stmt->execute([$template_id]);
                        $old_template = $stmt->fetch();
                        if ($old_template && file_exists(ROOT_PATH . '/' . $old_template['image_path'])) {
                            unlink(ROOT_PATH . '/' . $old_template['image_path']);
                        }
                        $stmt = $pdo->prepare("UPDATE templates SET festival_id = ?, image_path = ?, name = ?, is_active = ? WHERE id = ?");
                        $stmt->execute([$festival_id, $image_path, $name, $is_active, $template_id]);
                    }
                } else {
                    $stmt = $pdo->prepare("UPDATE templates SET festival_id = ?, name = ?, is_active = ? WHERE id = ?");
                    $stmt->execute([$festival_id, $name, $is_active, $template_id]);
                }
                setFlashMessage('Template updated successfully!', 'success');
                redirect('templates.php');
            }
        }
    }
}

// Handle delete action
if ($action === 'delete' && $template_id) {
    if (validateCSRFToken($_GET['token'] ?? '')) {
        // Get template info to delete file
        $stmt = $pdo->prepare("SELECT image_path FROM templates WHERE id = ?");
        $stmt->execute([$template_id]);
        $template = $stmt->fetch();
        
        $stmt = $pdo->prepare("DELETE FROM templates WHERE id = ?");
        if ($stmt->execute([$template_id])) {
            // Delete image file
            if ($template && file_exists(ROOT_PATH . '/' . $template['image_path'])) {
                unlink(ROOT_PATH . '/' . $template['image_path']);
            }
            setFlashMessage('Template deleted successfully!', 'success');
        } else {
            setFlashMessage('Failed to delete template.', 'error');
        }
    }
    redirect('templates.php');
}

// Get template data for editing
$template = null;
if ($action === 'edit' && $template_id) {
    $stmt = $pdo->prepare("SELECT * FROM templates WHERE id = ?");
    $stmt->execute([$template_id]);
    $template = $stmt->fetch();
    if (!$template) {
        redirect('templates.php');
    }
}

// Get all festivals for dropdown
$festivals = getAllFestivals($pdo);

// Get templates for listing
$templates = [];
if ($action === 'list') {
    $sql = "SELECT t.*, f.name as festival_name 
            FROM templates t 
            JOIN festivals f ON t.festival_id = f.id";
    $params = [];
    
    if ($festival_filter) {
        $sql .= " WHERE t.festival_id = ?";
        $params[] = $festival_filter;
    }
    
    $sql .= " ORDER BY f.date ASC, t.name ASC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $templates = $stmt->fetchAll();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Management - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="<?php echo getAssetUrl('css/style.css'); ?>" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 admin-sidebar">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">🎉 <?php echo APP_NAME; ?></h5>
                        <small class="text-muted">Admin Panel</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="festivals.php">
                                <i class="fas fa-calendar-alt me-2"></i>Festivals
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="templates.php">
                                <i class="fas fa-images me-2"></i>Templates
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="fas fa-users me-2"></i>Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="payments.php">
                                <i class="fas fa-credit-card me-2"></i>Payments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="fas fa-cog me-2"></i>Settings
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 admin-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-images me-2"></i>Template Management
                    </h1>
                    <?php if ($action === 'list'): ?>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <a href="templates.php?action=add" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add Template
                            </a>
                        </div>
                    <?php endif; ?>
                </div>

                <?php displayFlashMessage(); ?>

                <?php if ($action === 'list'): ?>
                    <!-- Filter -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <select class="form-select" id="festival-filter" onchange="filterByFestival(this.value)">
                                <option value="">All Festivals</option>
                                <?php foreach ($festivals as $fest): ?>
                                    <option value="<?php echo $fest['id']; ?>" <?php echo $festival_filter == $fest['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($fest['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <!-- Template Grid -->
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <?php if (empty($templates)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-images fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No templates found</h5>
                                    <p class="text-muted">Start by adding your first template.</p>
                                    <a href="templates.php?action=add" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>Add Template
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="row">
                                    <?php foreach ($templates as $tmpl): ?>
                                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                                            <div class="card h-100 border-0 shadow-sm">
                                                <div class="position-relative">
                                                    <img src="<?php echo getUploadUrl($tmpl['image_path']); ?>" 
                                                         class="card-img-top" alt="<?php echo htmlspecialchars($tmpl['name']); ?>"
                                                         style="height: 200px; object-fit: cover;">
                                                    <?php if (!$tmpl['is_active']): ?>
                                                        <div class="position-absolute top-0 end-0 m-2">
                                                            <span class="badge bg-warning">Inactive</span>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="card-body">
                                                    <h6 class="card-title"><?php echo htmlspecialchars($tmpl['name']); ?></h6>
                                                    <p class="card-text">
                                                        <small class="text-muted">
                                                            <i class="fas fa-calendar me-1"></i>
                                                            <?php echo htmlspecialchars($tmpl['festival_name']); ?>
                                                        </small>
                                                    </p>
                                                </div>
                                                <div class="card-footer bg-white border-0">
                                                    <div class="btn-group w-100" role="group">
                                                        <a href="templates.php?action=edit&id=<?php echo $tmpl['id']; ?>" 
                                                           class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="templates.php?action=delete&id=<?php echo $tmpl['id']; ?>&token=<?php echo generateCSRFToken(); ?>" 
                                                           class="btn btn-sm btn-outline-danger delete-btn">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                <?php elseif ($action === 'add' || $action === 'edit'): ?>
                    <!-- Add/Edit Template Form -->
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-white">
                                    <h5 class="mb-0">
                                        <?php echo $action === 'add' ? 'Add New Template' : 'Edit Template'; ?>
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($errors)): ?>
                                        <div class="alert alert-danger">
                                            <ul class="mb-0">
                                                <?php foreach ($errors as $error): ?>
                                                    <li><?php echo htmlspecialchars($error); ?></li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    <?php endif; ?>

                                    <form method="POST" enctype="multipart/form-data">
                                        <?php echo getCSRFTokenField(); ?>
                                        
                                        <div class="mb-3">
                                            <label for="festival_id" class="form-label">Festival *</label>
                                            <select class="form-select" id="festival_id" name="festival_id" required>
                                                <option value="">Select Festival</option>
                                                <?php foreach ($festivals as $fest): ?>
                                                    <option value="<?php echo $fest['id']; ?>" 
                                                            <?php echo ($template['festival_id'] ?? $_POST['festival_id'] ?? '') == $fest['id'] ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($fest['name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Template Name *</label>
                                            <input type="text" class="form-control" id="name" name="name" 
                                                   value="<?php echo htmlspecialchars($template['name'] ?? $_POST['name'] ?? ''); ?>" 
                                                   required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="template_image" class="form-label">
                                                Template Image <?php echo $action === 'add' ? '*' : '(Optional)'; ?>
                                            </label>
                                            <input type="file" class="form-control" id="template_image" name="template_image" 
                                                   accept="image/*" <?php echo $action === 'add' ? 'required' : ''; ?>>
                                            <div class="form-text">
                                                Recommended size: 1200x800px. Formats: JPG, PNG
                                            </div>
                                            <?php if ($action === 'edit' && $template): ?>
                                                <div class="mt-2">
                                                    <small class="text-muted">Current image:</small><br>
                                                    <img src="<?php echo getUploadUrl($template['image_path']); ?>" 
                                                         alt="Current template" style="max-height: 100px;" class="rounded">
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="mb-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                                       <?php echo ($template['is_active'] ?? $_POST['is_active'] ?? true) ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="is_active">
                                                    Active (available for users)
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="d-flex gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>
                                                <?php echo $action === 'add' ? 'Add Template' : 'Update Template'; ?>
                                            </button>
                                            <a href="templates.php" class="btn btn-secondary">
                                                <i class="fas fa-times me-2"></i>Cancel
                                            </a>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
    
    <script>
        function filterByFestival(festivalId) {
            if (festivalId) {
                window.location.href = 'templates.php?festival=' + festivalId;
            } else {
                window.location.href = 'templates.php';
            }
        }
    </script>
</body>
</html>
