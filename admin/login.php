<?php
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../config/session.php';
require_once '../includes/functions.php';

// Redirect if already logged in as admin
if (isAdminLoggedIn()) {
    redirect('index.php');
}

$errors = [];
$login_attempts = $_SESSION['admin_login_attempts'] ?? 0;
$last_attempt_time = $_SESSION['admin_last_attempt_time'] ?? 0;

// Check if admin is locked out
$lockout_remaining = 0;
if ($login_attempts >= MAX_LOGIN_ATTEMPTS) {
    $lockout_remaining = LOGIN_LOCKOUT_TIME - (time() - $last_attempt_time);
    if ($lockout_remaining <= 0) {
        // Reset attempts after lockout period
        unset($_SESSION['admin_login_attempts']);
        unset($_SESSION['admin_last_attempt_time']);
        $login_attempts = 0;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && $lockout_remaining <= 0) {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid request. Please try again.';
    } else {
        // Get and sanitize input
        $username = sanitize_input($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        
        // Validation
        if (empty($username)) {
            $errors[] = 'Username is required.';
        }
        
        if (empty($password)) {
            $errors[] = 'Password is required.';
        }
        
        // Authenticate admin if no errors
        if (empty($errors)) {
            $stmt = $pdo->prepare("SELECT * FROM admin_users WHERE username = ?");
            $stmt->execute([$username]);
            $admin = $stmt->fetch();
            
            if ($admin && verify_password($password, $admin['password'])) {
                // Successful login
                loginAdmin($admin['id'], $admin['username'], $admin['email']);
                
                // Reset login attempts
                unset($_SESSION['admin_login_attempts']);
                unset($_SESSION['admin_last_attempt_time']);
                
                // Redirect to admin dashboard
                redirect('index.php');
            } else {
                // Failed login
                $errors[] = 'Invalid username or password.';
                
                // Increment login attempts
                $_SESSION['admin_login_attempts'] = $login_attempts + 1;
                $_SESSION['admin_last_attempt_time'] = time();
            }
        }
    }
}

// Display flash messages
$flash = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="<?php echo getAssetUrl('css/style.css'); ?>" rel="stylesheet">
</head>
<body class="bg-dark">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card shadow-lg border-0 mt-5">
                    <div class="card-header bg-dark text-white text-center py-4">
                        <h2 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>Admin Panel
                        </h2>
                        <p class="mb-0"><?php echo APP_NAME; ?></p>
                    </div>
                    <div class="card-body p-5">
                        <?php if ($flash): ?>
                            <div class="alert alert-<?php echo $flash['type'] === 'error' ? 'danger' : $flash['type']; ?>">
                                <?php echo htmlspecialchars($flash['message']); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($lockout_remaining > 0): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-lock me-2"></i>
                                Too many failed login attempts. Please try again in 
                                <span id="lockout-timer"><?php echo $lockout_remaining; ?></span> seconds.
                            </div>
                        <?php else: ?>
                            <form method="POST" action="">
                                <?php echo getCSRFTokenField(); ?>
                                
                                <div class="mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-user me-2"></i>Username
                                    </label>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" 
                                           required autofocus>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-2"></i>Password
                                    </label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                                
                                <?php if ($login_attempts > 0): ?>
                                    <div class="alert alert-info">
                                        <small>
                                            Failed attempts: <?php echo $login_attempts; ?>/<?php echo MAX_LOGIN_ATTEMPTS; ?>
                                        </small>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-dark btn-lg">
                                        <i class="fas fa-sign-in-alt me-2"></i>Login to Admin Panel
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <a href="../index.php" class="text-muted">
                                <i class="fas fa-arrow-left me-2"></i>Back to Main Site
                            </a>
                        </div>
                        
                        <!-- Demo Credentials Info -->
                        <div class="mt-4 p-3 bg-light rounded">
                            <h6 class="text-muted mb-2">Demo Credentials:</h6>
                            <p class="mb-1"><strong>Username:</strong> admin</p>
                            <p class="mb-0"><strong>Password:</strong> admin123</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
    
    <?php if ($lockout_remaining > 0): ?>
    <script>
        // Countdown timer for lockout
        var timeLeft = <?php echo $lockout_remaining; ?>;
        var timer = setInterval(function() {
            timeLeft--;
            document.getElementById('lockout-timer').textContent = timeLeft;
            
            if (timeLeft <= 0) {
                clearInterval(timer);
                location.reload();
            }
        }, 1000);
    </script>
    <?php endif; ?>
</body>
</html>
