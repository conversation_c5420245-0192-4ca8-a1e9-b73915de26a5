<?php
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../config/session.php';
require_once '../includes/functions.php';

// Require admin login
requireAdminLogin();

$errors = [];
$success = false;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid request token.';
    } else {
        $settings = [
            'kit_price' => floatval($_POST['kit_price'] ?? 0),
            'watermark_enabled' => isset($_POST['watermark_enabled']) ? '1' : '0',
            'site_name' => sanitize_input($_POST['site_name'] ?? ''),
            'max_logo_size' => intval($_POST['max_logo_size'] ?? 0),
            'admin_email' => sanitize_input($_POST['admin_email'] ?? ''),
            'items_per_page' => intval($_POST['items_per_page'] ?? 25),
            'maintenance_mode' => isset($_POST['maintenance_mode']) ? '1' : '0',
            'registration_enabled' => isset($_POST['registration_enabled']) ? '1' : '0',
            'social_posting_enabled' => isset($_POST['social_posting_enabled']) ? '1' : '0'
        ];
        
        // Validation
        if ($settings['kit_price'] <= 0) {
            $errors[] = 'Kit price must be greater than 0.';
        }
        if (empty($settings['site_name'])) {
            $errors[] = 'Site name is required.';
        }
        if ($settings['max_logo_size'] <= 0) {
            $errors[] = 'Maximum logo size must be greater than 0.';
        }
        if (!validate_email($settings['admin_email'])) {
            $errors[] = 'Please enter a valid admin email address.';
        }
        if ($settings['items_per_page'] < 10 || $settings['items_per_page'] > 100) {
            $errors[] = 'Items per page must be between 10 and 100.';
        }
        
        // Save settings if no errors
        if (empty($errors)) {
            $saved_count = 0;
            foreach ($settings as $key => $value) {
                if (updateSetting($pdo, $key, $value)) {
                    $saved_count++;
                }
            }
            
            if ($saved_count > 0) {
                $success = true;
                setFlashMessage('Settings updated successfully!', 'success');
            } else {
                $errors[] = 'Failed to update settings.';
            }
        }
    }
}

// Get current settings
$current_settings = [
    'kit_price' => getSetting($pdo, 'kit_price', DEFAULT_KIT_PRICE),
    'watermark_enabled' => getSetting($pdo, 'watermark_enabled', '1'),
    'site_name' => getSetting($pdo, 'site_name', APP_NAME),
    'max_logo_size' => getSetting($pdo, 'max_logo_size', MAX_LOGO_SIZE),
    'admin_email' => getSetting($pdo, 'admin_email', ADMIN_EMAIL),
    'items_per_page' => getSetting($pdo, 'items_per_page', ITEMS_PER_PAGE),
    'maintenance_mode' => getSetting($pdo, 'maintenance_mode', '0'),
    'registration_enabled' => getSetting($pdo, 'registration_enabled', '1'),
    'social_posting_enabled' => getSetting($pdo, 'social_posting_enabled', '1')
];

// Get system info
$system_info = [
    'php_version' => phpversion(),
    'mysql_version' => $pdo->query('SELECT VERSION()')->fetchColumn(),
    'gd_enabled' => extension_loaded('gd'),
    'zip_enabled' => extension_loaded('zip'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time')
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="<?php echo getAssetUrl('css/style.css'); ?>" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 admin-sidebar">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">🎉 <?php echo APP_NAME; ?></h5>
                        <small class="text-muted">Admin Panel</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="festivals.php">
                                <i class="fas fa-calendar-alt me-2"></i>Festivals
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="templates.php">
                                <i class="fas fa-images me-2"></i>Templates
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="fas fa-users me-2"></i>Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="payments.php">
                                <i class="fas fa-credit-card me-2"></i>Payments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="settings.php">
                                <i class="fas fa-cog me-2"></i>Settings
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 admin-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-cog me-2"></i>Settings
                    </h1>
                </div>

                <?php displayFlashMessage(); ?>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-lg-8">
                        <!-- Application Settings -->
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="fas fa-sliders-h me-2"></i>Application Settings
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <?php echo getCSRFTokenField(); ?>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="site_name" class="form-label">Site Name</label>
                                                <input type="text" class="form-control" id="site_name" name="site_name" 
                                                       value="<?php echo htmlspecialchars($current_settings['site_name']); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="admin_email" class="form-label">Admin Email</label>
                                                <input type="email" class="form-control" id="admin_email" name="admin_email" 
                                                       value="<?php echo htmlspecialchars($current_settings['admin_email']); ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="kit_price" class="form-label">Kit Price ($)</label>
                                                <input type="number" class="form-control" id="kit_price" name="kit_price" 
                                                       value="<?php echo $current_settings['kit_price']; ?>" 
                                                       min="0.01" step="0.01" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="max_logo_size" class="form-label">Max Logo Size (bytes)</label>
                                                <input type="number" class="form-control" id="max_logo_size" name="max_logo_size" 
                                                       value="<?php echo $current_settings['max_logo_size']; ?>" 
                                                       min="1048576" required>
                                                <div class="form-text">Current: <?php echo formatFileSize($current_settings['max_logo_size']); ?></div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="items_per_page" class="form-label">Items Per Page (Admin Tables)</label>
                                        <input type="number" class="form-control" id="items_per_page" name="items_per_page" 
                                               value="<?php echo $current_settings['items_per_page']; ?>" 
                                               min="10" max="100" required>
                                    </div>
                                    
                                    <hr>
                                    
                                    <h6 class="mb-3">Feature Settings</h6>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="watermark_enabled" name="watermark_enabled" 
                                                       <?php echo $current_settings['watermark_enabled'] ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="watermark_enabled">
                                                    Enable Watermark on Generated Images
                                                </label>
                                            </div>
                                            
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="registration_enabled" name="registration_enabled" 
                                                       <?php echo $current_settings['registration_enabled'] ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="registration_enabled">
                                                    Allow User Registration
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="social_posting_enabled" name="social_posting_enabled" 
                                                       <?php echo $current_settings['social_posting_enabled'] ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="social_posting_enabled">
                                                    Enable Social Media Auto-Posting
                                                </label>
                                            </div>
                                            
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode" 
                                                       <?php echo $current_settings['maintenance_mode'] ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="maintenance_mode">
                                                    <span class="text-warning">Maintenance Mode</span>
                                                </label>
                                                <div class="form-text">Disables site for regular users</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Save Settings
                                        </button>
                                        <button type="reset" class="btn btn-outline-secondary">
                                            <i class="fas fa-undo me-2"></i>Reset
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <!-- System Information -->
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-server me-2"></i>System Information
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong>PHP Version:</strong><br>
                                    <span class="text-muted"><?php echo $system_info['php_version']; ?></span>
                                </div>
                                
                                <div class="mb-3">
                                    <strong>MySQL Version:</strong><br>
                                    <span class="text-muted"><?php echo $system_info['mysql_version']; ?></span>
                                </div>
                                
                                <div class="mb-3">
                                    <strong>Extensions:</strong><br>
                                    <span class="badge bg-<?php echo $system_info['gd_enabled'] ? 'success' : 'danger'; ?>">
                                        GD <?php echo $system_info['gd_enabled'] ? 'Enabled' : 'Disabled'; ?>
                                    </span>
                                    <span class="badge bg-<?php echo $system_info['zip_enabled'] ? 'success' : 'danger'; ?>">
                                        ZIP <?php echo $system_info['zip_enabled'] ? 'Enabled' : 'Disabled'; ?>
                                    </span>
                                </div>
                                
                                <div class="mb-3">
                                    <strong>Upload Limits:</strong><br>
                                    <small class="text-muted">
                                        Max File Size: <?php echo $system_info['upload_max_filesize']; ?><br>
                                        Post Max Size: <?php echo $system_info['post_max_size']; ?>
                                    </small>
                                </div>
                                
                                <div class="mb-0">
                                    <strong>Memory & Time:</strong><br>
                                    <small class="text-muted">
                                        Memory Limit: <?php echo $system_info['memory_limit']; ?><br>
                                        Max Execution: <?php echo $system_info['max_execution_time']; ?>s
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">
                                    <i class="fas fa-tools me-2"></i>Quick Actions
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="clearCache()">
                                        <i class="fas fa-broom me-2"></i>Clear Cache
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="testEmail()">
                                        <i class="fas fa-envelope me-2"></i>Test Email
                                    </button>
                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="backupDatabase()">
                                        <i class="fas fa-database me-2"></i>Backup Database
                                    </button>
                                    <a href="../index.php" target="_blank" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-external-link-alt me-2"></i>View Site
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="<?php echo getAssetUrl('js/main.js'); ?>"></script>
    
    <script>
        function clearCache() {
            if (confirm('Are you sure you want to clear the cache?')) {
                // Implement cache clearing logic
                alert('Cache cleared successfully!');
            }
        }
        
        function testEmail() {
            // Implement email testing logic
            alert('Test email sent to admin address!');
        }
        
        function backupDatabase() {
            if (confirm('Are you sure you want to create a database backup?')) {
                // Implement database backup logic
                alert('Database backup created successfully!');
            }
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            var k = 1024;
            var sizes = ['Bytes', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // Update file size display when input changes
        $('#max_logo_size').on('input', function() {
            var bytes = parseInt($(this).val());
            var formatted = formatFileSize(bytes);
            $(this).next('.form-text').text('Current: ' + formatted);
        });
    </script>
</body>
</html>
