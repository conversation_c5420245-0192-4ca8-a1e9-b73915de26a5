<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kit Generator JavaScript Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .file-upload-area {
            border: 2px dashed #ddd;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
            min-height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .file-upload-area:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .file-upload-area.dragover {
            border-color: #28a745;
            background: #d4edda;
            transform: scale(1.02);
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1>🧪 Kit Generator JavaScript Test</h1>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <h3>Test Form</h3>
                        <form id="kit-generator-form" enctype="multipart/form-data">
                            <div class="mb-4">
                                <label class="form-label">
                                    <i class="fas fa-image me-2"></i>Business Logo <span class="text-danger">*</span>
                                </label>
                                <div class="file-upload-area">
                                    <input type="file" id="logo_file" name="logo" accept="image/*" style="display: none;" required>
                                    <div class="text-center">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <p class="mb-2">Click to upload your business logo</p>
                                        <small class="text-muted">PNG, JPG up to 5MB</small>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <button type="button" class="btn btn-sm btn-outline-secondary" id="use-previous-logo">
                                        Use Previous Logo (Test)
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Business Name</label>
                                <input type="text" class="form-control" name="business_name" value="Test Business" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Tagline</label>
                                <input type="text" class="form-control" name="tagline" value="Test Tagline">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Contact Info</label>
                                <input type="text" class="form-control" name="contact_info" value="<EMAIL>">
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-magic me-2"></i>Test Submit
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5>Debug Info</h5>
                        <div id="debug-info">
                            <p>File input found: <span id="file-input-status">❌</span></p>
                            <p>File selected: <span id="file-selected-status">❌</span></p>
                            <p>Form ready: <span id="form-ready-status">❌</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            console.log('Document ready');
            
            // Check if elements exist
            var fileInput = document.getElementById('logo_file');
            var form = document.getElementById('kit-generator-form');
            
            $('#file-input-status').text(fileInput ? '✅' : '❌');
            $('#form-ready-status').text(form ? '✅' : '❌');
            
            // Use previous logo button
            $(document).on('click', '#use-previous-logo', function() {
                console.log('Use previous logo clicked');
                var fileInput = document.getElementById('logo_file');
                if (fileInput) {
                    fileInput.removeAttribute('required');
                }
                $('.file-upload-area').html('<div class="text-center text-success"><i class="fas fa-check-circle fa-2x mb-2"></i><p>Using previously uploaded logo</p></div>');
                $('#kit-generator-form').data('use-previous-logo', true);
                $('#file-selected-status').text('✅ (Previous)');
            });

            // File upload area click handler
            $('.file-upload-area').click(function(e) {
                e.preventDefault();
                console.log('Upload area clicked');
                var fileInput = document.getElementById('logo_file');
                if (fileInput) {
                    fileInput.click();
                } else {
                    console.error('Logo file input not found');
                }
            });

            // File upload change handler
            $(document).on('change', '#logo_file', function() {
                console.log('File input changed');
                var file = this.files[0];
                if (file) {
                    console.log('File selected:', file.name);
                    $('#file-selected-status').text('✅');
                    
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        $('.file-upload-area').html(
                            '<div class="text-center">' +
                            '<img src="' + e.target.result + '" style="max-width: 100px; max-height: 100px; border-radius: 5px;" class="mb-2">' +
                            '<p class="mb-0"><strong>' + file.name + '</strong></p>' +
                            '<small class="text-muted">' + (file.size / 1024 / 1024).toFixed(2) + ' MB</small>' +
                            '<br><small class="text-success">✓ Ready to upload</small>' +
                            '</div>'
                        );
                    };
                    reader.readAsDataURL(file);
                } else {
                    $('#file-selected-status').text('❌');
                }
            });

            // Form submission
            $('#kit-generator-form').submit(function(e) {
                e.preventDefault();
                console.log('Form submitted');
                
                var $form = $(this);
                
                // Validate file upload
                var fileInput = document.getElementById('logo_file');
                if (!fileInput) {
                    console.error('Logo file input not found');
                    alert('Error: Logo upload field not found. Please refresh the page.');
                    return;
                }
                
                var usePreviousLogo = $form.data('use-previous-logo');
                console.log('Use previous logo:', usePreviousLogo);
                console.log('Files:', fileInput.files);
                
                if (!usePreviousLogo && (!fileInput.files || fileInput.files.length === 0)) {
                    alert('Please upload a business logo or click "Use Previous Logo" if you have one.');
                    return;
                }
                
                // Create FormData for file upload
                var formData = new FormData(this);
                
                // Debug: Log form data
                console.log('Form submission data:');
                for (var pair of formData.entries()) {
                    console.log(pair[0] + ': ' + pair[1]);
                }
                
                alert('Form validation passed! Check console for details.');
            });
        });
    </script>
</body>
</html>
