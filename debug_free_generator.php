<?php
session_start();
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

// Simple login for testing (remove in production)
if (!isLoggedIn()) {
    // Create a test session
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'Test User';
    $_SESSION['user_email'] = '<EMAIL>';
}

echo "<h1>Free Generator Debug</h1>";

// Test 1: Check if user is logged in
echo "<h2>1. User Session Test</h2>";
if (isLoggedIn()) {
    echo "✅ User is logged in<br>";
    echo "User ID: " . getCurrentUserId() . "<br>";
    echo "User Name: " . ($_SESSION['user_name'] ?? 'Not set') . "<br>";
} else {
    echo "❌ User is NOT logged in<br>";
}

// Test 2: Check festivals
echo "<h2>2. Festivals Test</h2>";
try {
    $festivals = getAllFestivals($pdo);
    echo "✅ Found " . count($festivals) . " festivals<br>";
    foreach ($festivals as $festival) {
        echo "- {$festival['name']} (ID: {$festival['id']})<br>";
    }
} catch (Exception $e) {
    echo "❌ Failed to get festivals: " . $e->getMessage() . "<br>";
}

// Test 3: Check templates for first festival
echo "<h2>3. Templates Test</h2>";
if (!empty($festivals)) {
    $first_festival = $festivals[0];
    try {
        $templates = getTemplatesByFestival($pdo, $first_festival['id']);
        echo "✅ Found " . count($templates) . " templates for '{$first_festival['name']}'<br>";
        foreach ($templates as $template) {
            $template_path = ROOT_PATH . '/' . $template['image_path'];
            $exists = file_exists($template_path) ? '✅' : '❌';
            echo "- {$template['name']} (ID: {$template['id']}) $exists<br>";
            echo "  Path: {$template['image_path']}<br>";
        }
    } catch (Exception $e) {
        echo "❌ Failed to get templates: " . $e->getMessage() . "<br>";
    }
}

// Test 4: Simulate image generation
echo "<h2>4. Image Generation Simulation</h2>";
if (!empty($templates)) {
    $test_template = $templates[0];
    $template_path = ROOT_PATH . '/' . $test_template['image_path'];
    
    if (file_exists($template_path)) {
        echo "Using template: {$test_template['name']}<br>";
        echo "Template path: $template_path<br>";
        
        // Test parameters
        $user_id = getCurrentUserId();
        $output_filename = 'debug_generated_' . $user_id . '_' . time() . '.jpg';
        $output_path = ROOT_PATH . '/' . UPLOAD_DIR_GENERATED . $output_filename;
        $test_message = "Debug Test Message";
        
        echo "Output path: $output_path<br>";
        echo "Test message: $test_message<br>";
        
        // Check output directory
        $output_dir = dirname($output_path);
        if (!is_dir($output_dir)) {
            echo "Creating output directory...<br>";
            mkdir($output_dir, 0755, true);
        }
        
        if (is_writable($output_dir)) {
            echo "✅ Output directory is writable<br>";
            
            // Try to generate image
            $result = generateFestivalImage($template_path, null, $test_message, $output_path);
            
            if ($result['success']) {
                echo "✅ Image generation successful!<br>";
                if (file_exists($output_path)) {
                    $size = getimagesize($output_path);
                    echo "Generated file size: " . filesize($output_path) . " bytes<br>";
                    echo "Dimensions: {$size[0]}x{$size[1]}<br>";
                    echo "Preview URL: " . getUploadUrl(UPLOAD_DIR_GENERATED . $output_filename) . "<br>";
                    echo "<img src='" . getUploadUrl(UPLOAD_DIR_GENERATED . $output_filename) . "' style='max-width: 300px; border: 1px solid #ccc;'><br>";
                    
                    // Clean up
                    echo "<br><a href='#' onclick='cleanupFile(\"$output_filename\")'>Clean up test file</a><br>";
                } else {
                    echo "❌ Generated file not found<br>";
                }
            } else {
                echo "❌ Image generation failed: " . $result['message'] . "<br>";
            }
        } else {
            echo "❌ Output directory is NOT writable<br>";
        }
    } else {
        echo "❌ Template file not found: $template_path<br>";
    }
}

// Test 5: CSRF Token
echo "<h2>5. CSRF Token Test</h2>";
$csrf_token = generateCSRFToken();
echo "✅ CSRF Token generated: " . substr($csrf_token, 0, 10) . "...<br>";
echo "Token field: " . getCSRFTokenField() . "<br>";

echo "<h2>Debug Complete</h2>";
echo "<p><a href='free_generator.php'>→ Go to Free Generator</a></p>";
echo "<p><a href='test_image_generation.php'>→ Run System Test</a></p>";
echo "<p><a href='index.php'>← Back to Homepage</a></p>";
?>

<script>
function cleanupFile(filename) {
    if (confirm('Delete test file?')) {
        fetch('cleanup_test_file.php?file=' + encodeURIComponent(filename))
            .then(response => response.text())
            .then(data => {
                alert('File cleaned up');
                location.reload();
            });
    }
}
</script>
