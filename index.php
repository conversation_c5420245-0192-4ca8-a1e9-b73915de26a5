<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if user is logged in
$user_logged_in = isset($_SESSION['user_id']);
$user_name = $user_logged_in ? $_SESSION['user_name'] : '';

// Get featured festivals
$featured_festivals = getFeaturedFestivals($pdo);

// Sample festival images for showcase
$sample_images = [
    ['name' => 'New Year 2024', 'image' => 'assets/images/showcase-newyear.jpg'],
    ['name' => 'Valentine\'s Day', 'image' => 'assets/images/showcase-valentine.jpg'],
    ['name' => 'Christmas Joy', 'image' => 'assets/images/showcase-christmas.jpg'],
    ['name' => 'Diwali Festival', 'image' => 'assets/images/showcase-diwali.jpg'],
    ['name' => 'Eid Mubarak', 'image' => 'assets/images/showcase-eid.jpg'],
    ['name' => 'Birthday Celebration', 'image' => 'assets/images/showcase-birthday.jpg']
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FestivalKit - Create Beautiful Festival Images</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif !important;
            overflow-x: hidden;
        }

        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1.5" fill="rgba(255,255,255,0.08)"/><circle cx="50" cy="10" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.4;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .floating-shapes::before,
        .floating-shapes::after {
            content: '';
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .floating-shapes::before {
            width: 300px;
            height: 300px;
            top: 10%;
            right: 10%;
            animation-delay: 0s;
        }

        .floating-shapes::after {
            width: 200px;
            height: 200px;
            bottom: 20%;
            left: 10%;
            animation-delay: 3s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-30px) rotate(180deg); }
        }

        .modern-navbar {
            background: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(20px);
            border: none !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
            padding: 20px 0 !important;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .navbar-brand {
            font-weight: 900 !important;
            font-size: 2rem !important;
            color: white !important;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 600 !important;
            padding: 12px 25px !important;
            border-radius: 30px !important;
            transition: all 0.3s ease !important;
            margin: 0 5px;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            transform: translateY(-2px);
        }

        .btn-cta {
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%) !important;
            border: none !important;
            padding: 12px 30px !important;
            border-radius: 50px !important;
            font-weight: 700 !important;
            color: white !important;
            text-transform: uppercase !important;
            letter-spacing: 1px !important;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4) !important;
            transition: all 0.3s ease !important;
        }

        .btn-cta:hover {
            transform: translateY(-3px) !important;
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.6) !important;
            color: white !important;
        }

        .hero-content h1 {
            font-size: 4.5rem !important;
            font-weight: 900 !important;
            color: white !important;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            margin-bottom: 30px !important;
            line-height: 1.1;
        }

        .hero-content p {
            font-size: 1.4rem !important;
            color: rgba(255, 255, 255, 0.9) !important;
            margin-bottom: 40px !important;
            font-weight: 400;
        }

        .hero-buttons .btn {
            margin: 10px 15px;
            padding: 18px 40px;
            font-size: 1.1rem;
            font-weight: 700;
            border-radius: 50px;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }

        .btn-primary-hero {
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%) !important;
            border: none !important;
            color: white !important;
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.4) !important;
        }

        .btn-primary-hero:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 20px 45px rgba(255, 107, 107, 0.6) !important;
            color: white !important;
        }

        .btn-outline-hero {
            background: transparent !important;
            border: 3px solid rgba(255, 255, 255, 0.3) !important;
            color: white !important;
            backdrop-filter: blur(10px);
        }

        .btn-outline-hero:hover {
            background: rgba(255, 255, 255, 0.2) !important;
            border-color: rgba(255, 255, 255, 0.5) !important;
            color: white !important;
            transform: translateY(-5px) !important;
        }

        .section-modern {
            padding: 100px 0;
            position: relative;
        }

        .section-title {
            font-size: 3rem !important;
            font-weight: 800 !important;
            text-align: center;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .section-subtitle {
            font-size: 1.3rem;
            color: #666;
            text-align: center;
            margin-bottom: 80px;
            font-weight: 400;
        }

        .feature-card {
            background: white;
            border-radius: 30px;
            padding: 50px 40px;
            text-align: center;
            border: none;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .feature-card:hover {
            transform: translateY(-20px) scale(1.02);
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            font-size: 3rem;
            color: white;
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 20px 50px rgba(102, 126, 234, 0.6);
        }

        .feature-card h4 {
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 20px;
        }

        .feature-card p {
            color: #666;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .sample-gallery {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
        }

        .sample-image {
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            margin-bottom: 30px;
        }

        .sample-image:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .sample-image img {
            width: 100%;
            height: 250px;
            object-fit: cover;
            transition: all 0.3s ease;
        }

        .sample-image:hover img {
            transform: scale(1.05);
        }

        .sample-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .sample-image:hover .sample-overlay {
            opacity: 1;
        }

        .sample-overlay h5 {
            color: white;
            font-weight: 700;
            font-size: 1.3rem;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        @media (max-width: 768px) {
            .hero-content h1 {
                font-size: 3rem !important;
            }
            .hero-content p {
                font-size: 1.2rem !important;
            }
            .section-title {
                font-size: 2.2rem !important;
            }
            .feature-card {
                padding: 40px 30px;
                margin-bottom: 30px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg modern-navbar">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-sparkles me-2"></i>FestivalKit
            </a>
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <i class="fas fa-bars text-white"></i>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">Features</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#gallery">Gallery</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#pricing">Pricing</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if ($user_logged_in): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-cta" href="register.php">Get Started</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Ultra Modern Hero Section -->
    <section class="hero-section">
        <div class="floating-shapes"></div>
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6 hero-content" data-aos="fade-right">
                    <h1>Create Stunning<br>Festival Images</h1>
                    <p>Generate beautiful, professional festival images for your business with our AI-powered tools and advanced template designer.</p>
                    <div class="hero-buttons">
                        <?php if (!$user_logged_in): ?>
                            <a href="register.php" class="btn btn-primary-hero">
                                <i class="fas fa-rocket me-2"></i>Get Started Free
                            </a>
                            <a href="#gallery" class="btn btn-outline-hero">
                                <i class="fas fa-eye me-2"></i>View Samples
                            </a>
                        <?php else: ?>
                            <a href="dashboard.php" class="btn btn-primary-hero">
                                <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                            </a>
                            <a href="#gallery" class="btn btn-outline-hero">
                                <i class="fas fa-images me-2"></i>View Gallery
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-lg-6" data-aos="fade-left" data-aos-delay="200">
                    <div class="position-relative">
                        <img src="assets/images/hero-image.jpg" alt="Festival Images" class="img-fluid rounded-4 shadow-lg" style="border-radius: 30px !important;">
                        <div class="position-absolute top-0 start-0 w-100 h-100 bg-gradient rounded-4" style="background: linear-gradient(45deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%); border-radius: 30px !important;"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Festivals -->
    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">Upcoming Festivals</h2>
            <div class="row">
                <?php foreach ($featured_festivals as $festival): ?>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 shadow-sm">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo htmlspecialchars($festival['name']); ?></h5>
                                <p class="card-text"><?php echo htmlspecialchars($festival['description']); ?></p>
                                <p class="text-muted">
                                    <i class="fas fa-calendar"></i> 
                                    <?php echo date('F j, Y', strtotime($festival['date'])); ?>
                                </p>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Sample Gallery Section -->
    <section class="sample-gallery py-5" id="gallery">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">Sample Festival Images</h2>
                <p class="section-subtitle">See what you can create with FestivalKit</p>
            </div>
            <div class="row">
                <?php foreach ($sample_images as $sample): ?>
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="<?php echo array_search($sample, $sample_images) * 100; ?>">
                    <div class="sample-image">
                        <img src="<?php echo htmlspecialchars($sample['image']); ?>" alt="<?php echo htmlspecialchars($sample['name']); ?>">
                        <div class="sample-overlay">
                            <h5><?php echo htmlspecialchars($sample['name']); ?></h5>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <div class="text-center mt-5">
                <a href="register.php" class="btn btn-primary-hero btn-lg">
                    <i class="fas fa-rocket me-2"></i>Start Creating Now
                </a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="bg-light py-5" id="features">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">Why Choose FestivalKit?</h2>
                <p class="section-subtitle">Everything you need to create stunning festival content</p>
            </div>
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-magic"></i>
                        </div>
                        <h4>Easy to Use</h4>
                        <p>Simple interface that anyone can use. No design experience required. Create professional images in minutes.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <h4>AI-Powered Design</h4>
                        <p>Advanced AI creates unique designs based on your preferences. Generate unlimited variations with smart algorithms.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-paint-brush"></i>
                        </div>
                        <h4>Template Designer</h4>
                        <p>Pro users get access to our advanced Konva.js-powered template designer for unlimited creativity.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="400">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <h4>Instant Download</h4>
                        <p>Download your images immediately in high quality formats. Get ZIP packages with multiple variations.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="500">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-share-alt"></i>
                        </div>
                        <h4>Auto Social Posting</h4>
                        <p>Schedule automatic posts to your social media accounts. Never miss a festival celebration again.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="600">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <h4>Premium Features</h4>
                        <p>Unlock advanced features, unlimited generations, and priority support with our premium plans.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>FestivalKit</h5>
                    <p>Create beautiful festival images for your business.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>&copy; 2024 FestivalKit. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Initialize AOS (Animate On Scroll)
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navbar background change on scroll
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.modern-navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.backdropFilter = 'blur(20px)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.1)';
                navbar.style.backdropFilter = 'blur(20px)';
            }
        });
    </script>
</body>
</html>
