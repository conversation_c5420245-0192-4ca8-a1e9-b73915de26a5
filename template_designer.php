<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

// Require user login
requireLogin();

$user_id = getCurrentUserId();
$user = getUserById($pdo, $user_id);

// Check if user has premium access
if (($user['package_type'] ?? '') !== 'premium') {
    header('Location: upgrade.php');
    exit;
}

// Get festivals for template creation
$stmt = $pdo->query("SELECT * FROM festivals ORDER BY name");
$festivals = $stmt->fetchAll();

// Handle template save
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_template'])) {
    try {
        // Validate CSRF token
        if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid request token.');
        }
        
        $template_name = sanitize_input($_POST['template_name'] ?? '');
        $festival_id = intval($_POST['festival_id'] ?? 0);
        $template_data = $_POST['template_data'] ?? '';
        
        if (empty($template_name) || !$festival_id) {
            throw new Exception('Template name and festival are required.');
        }
        
        // Save template to database
        $stmt = $pdo->prepare("INSERT INTO user_templates (user_id, festival_id, name, template_data, created_at) VALUES (?, ?, ?, ?, NOW())");
        if ($stmt->execute([$user_id, $festival_id, $template_name, $template_data])) {
            setFlashMessage('Template saved successfully!', 'success');
        } else {
            throw new Exception('Failed to save template.');
        }
        
    } catch (Exception $e) {
        setFlashMessage($e->getMessage(), 'error');
    }
}

// Get user's saved templates
$stmt = $pdo->prepare("SELECT ut.*, f.name as festival_name FROM user_templates ut JOIN festivals f ON ut.festival_id = f.id WHERE ut.user_id = ? ORDER BY ut.created_at DESC");
$stmt->execute([$user_id]);
$user_templates = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Designer - FestivalKit Pro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .designer-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            margin: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .designer-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .designer-header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
        }
        
        .pro-badge {
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .toolbar {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .tool-group {
            margin-bottom: 20px;
        }
        
        .tool-group h6 {
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
        }
        
        .tool-btn {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 10px 15px;
            margin: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .tool-btn:hover, .tool-btn.active {
            border-color: #667eea;
            background: #f8f9ff;
            color: #667eea;
        }
        
        .color-picker {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            margin: 5px;
        }
        
        .template-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .template-card {
            background: white;
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .template-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .template-preview {
            width: 100%;
            height: 120px;
            background: #f8f9fa;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
        }
        
        #designCanvas {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .btn-designer {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 700;
            color: white;
            transition: all 0.3s ease;
        }
        
        .btn-designer:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <div class="designer-container">
        <?php displayFlashMessage(); ?>
        
        <div class="designer-header">
            <h1><i class="fas fa-paint-brush me-3"></i>Template Designer</h1>
            <p class="mb-2">Create custom festival templates with our advanced design tools</p>
            <span class="pro-badge"><i class="fas fa-crown me-1"></i>Pro Feature</span>
        </div>
        
        <div class="row">
            <div class="col-lg-3">
                <div class="toolbar">
                    <div class="tool-group">
                        <h6><i class="fas fa-shapes me-2"></i>Shapes</h6>
                        <div class="tool-btn" data-tool="rectangle">
                            <i class="fas fa-square"></i>Rectangle
                        </div>
                        <div class="tool-btn" data-tool="circle">
                            <i class="fas fa-circle"></i>Circle
                        </div>
                        <div class="tool-btn" data-tool="triangle">
                            <i class="fas fa-play"></i>Triangle
                        </div>
                        <div class="tool-btn" data-tool="star">
                            <i class="fas fa-star"></i>Star
                        </div>
                    </div>
                    
                    <div class="tool-group">
                        <h6><i class="fas fa-font me-2"></i>Text</h6>
                        <div class="tool-btn" data-tool="text">
                            <i class="fas fa-text-height"></i>Add Text
                        </div>
                        <div class="tool-btn" data-tool="heading">
                            <i class="fas fa-heading"></i>Heading
                        </div>
                    </div>
                    
                    <div class="tool-group">
                        <h6><i class="fas fa-image me-2"></i>Media</h6>
                        <div class="tool-btn" data-tool="image">
                            <i class="fas fa-image"></i>Add Image
                        </div>
                        <div class="tool-btn" data-tool="background">
                            <i class="fas fa-fill"></i>Background
                        </div>
                    </div>
                    
                    <div class="tool-group">
                        <h6><i class="fas fa-palette me-2"></i>Colors</h6>
                        <input type="color" class="color-picker" value="#667eea" data-property="fill">
                        <input type="color" class="color-picker" value="#764ba2" data-property="stroke">
                        <input type="color" class="color-picker" value="#ff6b6b" data-property="fill">
                        <input type="color" class="color-picker" value="#feca57" data-property="fill">
                        <input type="color" class="color-picker" value="#ffffff" data-property="fill">
                        <input type="color" class="color-picker" value="#000000" data-property="fill">
                    </div>
                    
                    <div class="tool-group">
                        <h6><i class="fas fa-cogs me-2"></i>Actions</h6>
                        <div class="tool-btn" data-action="clear">
                            <i class="fas fa-trash"></i>Clear All
                        </div>
                        <div class="tool-btn" data-action="undo">
                            <i class="fas fa-undo"></i>Undo
                        </div>
                        <div class="tool-btn" data-action="redo">
                            <i class="fas fa-redo"></i>Redo
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="canvas-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0"><i class="fas fa-canvas me-2"></i>Design Canvas</h5>
                        <div>
                            <button class="btn btn-outline-secondary btn-sm me-2" id="zoomOut">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <span id="zoomLevel">100%</span>
                            <button class="btn btn-outline-secondary btn-sm ms-2" id="zoomIn">
                                <i class="fas fa-search-plus"></i>
                            </button>
                        </div>
                    </div>
                    <div id="canvasContainer" style="text-align: center;">
                        <canvas id="designCanvas" width="600" height="400"></canvas>
                    </div>
                </div>
                
                <div class="text-center">
                    <button class="btn btn-designer me-2" id="saveTemplate">
                        <i class="fas fa-save me-2"></i>Save Template
                    </button>
                    <button class="btn btn-outline-primary me-2" id="previewTemplate">
                        <i class="fas fa-eye me-2"></i>Preview
                    </button>
                    <button class="btn btn-outline-success" id="exportTemplate">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                </div>
            </div>
            
            <div class="col-lg-3">
                <div class="toolbar">
                    <div class="tool-group">
                        <h6><i class="fas fa-layer-group me-2"></i>Layers</h6>
                        <div id="layersList" style="max-height: 200px; overflow-y: auto;">
                            <div class="layer-item p-2 border rounded mb-2">
                                <small class="text-muted">Background</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tool-group">
                        <h6><i class="fas fa-sliders-h me-2"></i>Properties</h6>
                        <div id="propertiesPanel">
                            <div class="mb-2">
                                <label class="form-label small">Width:</label>
                                <input type="number" class="form-control form-control-sm" id="objWidth">
                            </div>
                            <div class="mb-2">
                                <label class="form-label small">Height:</label>
                                <input type="number" class="form-control form-control-sm" id="objHeight">
                            </div>
                            <div class="mb-2">
                                <label class="form-label small">X Position:</label>
                                <input type="number" class="form-control form-control-sm" id="objX">
                            </div>
                            <div class="mb-2">
                                <label class="form-label small">Y Position:</label>
                                <input type="number" class="form-control form-control-sm" id="objY">
                            </div>
                            <div class="mb-2">
                                <label class="form-label small">Rotation:</label>
                                <input type="range" class="form-range" id="objRotation" min="0" max="360" value="0">
                            </div>
                            <div class="mb-2">
                                <label class="form-label small">Opacity:</label>
                                <input type="range" class="form-range" id="objOpacity" min="0" max="1" step="0.1" value="1">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h5><i class="fas fa-history me-2"></i>Your Saved Templates</h5>
                <div class="template-grid">
                    <?php foreach ($user_templates as $template): ?>
                    <div class="template-card" data-template-id="<?php echo $template['id']; ?>">
                        <div class="template-preview">
                            <i class="fas fa-image fa-2x text-muted"></i>
                        </div>
                        <h6 class="mb-1"><?php echo htmlspecialchars($template['name']); ?></h6>
                        <small class="text-muted"><?php echo htmlspecialchars($template['festival_name']); ?></small>
                        <div class="mt-2">
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="loadTemplate(<?php echo $template['id']; ?>)">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteTemplate(<?php echo $template['id']; ?>)">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    
                    <div class="template-card" style="border: 2px dashed #dee2e6;">
                        <div class="template-preview">
                            <i class="fas fa-plus fa-2x text-primary"></i>
                        </div>
                        <h6 class="mb-1">Create New</h6>
                        <small class="text-muted">Start from scratch</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <a href="dashboard.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>
    
    <!-- Save Template Modal -->
    <div class="modal fade" id="saveTemplateModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Save Template</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="template_data" id="templateDataInput">
                        
                        <div class="mb-3">
                            <label class="form-label">Template Name</label>
                            <input type="text" name="template_name" class="form-control" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Festival</label>
                            <select name="festival_id" class="form-select" required>
                                <option value="">Select Festival</option>
                                <?php foreach ($festivals as $festival): ?>
                                <option value="<?php echo $festival['id']; ?>"><?php echo htmlspecialchars($festival['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="save_template" class="btn btn-primary">Save Template</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://unpkg.com/konva@9/konva.min.js"></script>

    <script>
        let stage, layer;
        let selectedTool = null;
        let selectedObject = null;
        let history = [];
        let historyStep = -1;

        // Initialize Konva stage
        function initCanvas() {
            stage = new Konva.Stage({
                container: 'designCanvas',
                width: 600,
                height: 400
            });

            layer = new Konva.Layer();
            stage.add(layer);

            // Add background
            const background = new Konva.Rect({
                x: 0,
                y: 0,
                width: stage.width(),
                height: stage.height(),
                fill: '#ffffff',
                name: 'background'
            });
            layer.add(background);
            layer.draw();

            // Save initial state
            saveState();

            // Handle object selection
            stage.on('click tap', function (e) {
                if (e.target === stage) {
                    // Clicked on empty area
                    selectObject(null);
                    return;
                }

                selectObject(e.target);
            });
        }

        // Tool selection
        $('.tool-btn').click(function() {
            const tool = $(this).data('tool');
            const action = $(this).data('action');

            if (tool) {
                $('.tool-btn').removeClass('active');
                $(this).addClass('active');
                selectedTool = tool;
                stage.container().style.cursor = 'crosshair';
            } else if (action) {
                handleAction(action);
            }
        });

        // Handle canvas clicks for adding shapes
        stage.on('click tap', function (e) {
            if (!selectedTool || e.target !== stage) return;

            const pos = stage.getPointerPosition();
            let shape;

            switch (selectedTool) {
                case 'rectangle':
                    shape = new Konva.Rect({
                        x: pos.x - 50,
                        y: pos.y - 25,
                        width: 100,
                        height: 50,
                        fill: '#667eea',
                        stroke: '#764ba2',
                        strokeWidth: 2,
                        draggable: true
                    });
                    break;

                case 'circle':
                    shape = new Konva.Circle({
                        x: pos.x,
                        y: pos.y,
                        radius: 30,
                        fill: '#667eea',
                        stroke: '#764ba2',
                        strokeWidth: 2,
                        draggable: true
                    });
                    break;

                case 'triangle':
                    shape = new Konva.RegularPolygon({
                        x: pos.x,
                        y: pos.y,
                        sides: 3,
                        radius: 40,
                        fill: '#667eea',
                        stroke: '#764ba2',
                        strokeWidth: 2,
                        draggable: true
                    });
                    break;

                case 'star':
                    shape = new Konva.Star({
                        x: pos.x,
                        y: pos.y,
                        numPoints: 5,
                        innerRadius: 20,
                        outerRadius: 40,
                        fill: '#667eea',
                        stroke: '#764ba2',
                        strokeWidth: 2,
                        draggable: true
                    });
                    break;

                case 'text':
                    shape = new Konva.Text({
                        x: pos.x - 50,
                        y: pos.y - 10,
                        text: 'Sample Text',
                        fontSize: 20,
                        fontFamily: 'Poppins',
                        fill: '#333333',
                        draggable: true
                    });
                    break;

                case 'heading':
                    shape = new Konva.Text({
                        x: pos.x - 75,
                        y: pos.y - 15,
                        text: 'Heading',
                        fontSize: 30,
                        fontFamily: 'Poppins',
                        fontStyle: 'bold',
                        fill: '#333333',
                        draggable: true
                    });
                    break;
            }

            if (shape) {
                // Add transformer for editing
                const transformer = new Konva.Transformer({
                    nodes: [shape],
                    keepRatio: false,
                    enabledAnchors: ['top-left', 'top-right', 'bottom-left', 'bottom-right']
                });

                layer.add(shape);
                layer.add(transformer);
                layer.draw();

                selectObject(shape);
                saveState();
                updateLayers();
            }

            // Reset tool
            selectedTool = null;
            $('.tool-btn').removeClass('active');
            stage.container().style.cursor = 'default';
        });

        // Color picker functionality
        $('.color-picker').change(function() {
            const color = $(this).val();
            const property = $(this).data('property');

            if (selectedObject && selectedObject.name() !== 'background') {
                selectedObject[property](color);
                layer.draw();
                saveState();
            }
        });

        // Properties panel
        function updatePropertiesPanel() {
            if (!selectedObject) {
                $('#propertiesPanel input').val('');
                return;
            }

            $('#objWidth').val(selectedObject.width ? selectedObject.width() : '');
            $('#objHeight').val(selectedObject.height ? selectedObject.height() : '');
            $('#objX').val(Math.round(selectedObject.x()));
            $('#objY').val(Math.round(selectedObject.y()));
            $('#objRotation').val(selectedObject.rotation());
            $('#objOpacity').val(selectedObject.opacity());
        }

        // Properties change handlers
        $('#objWidth, #objHeight, #objX, #objY').change(function() {
            if (!selectedObject) return;

            const property = $(this).attr('id').replace('obj', '').toLowerCase();
            const value = parseFloat($(this).val());

            if (property === 'width' && selectedObject.width) {
                selectedObject.width(value);
            } else if (property === 'height' && selectedObject.height) {
                selectedObject.height(value);
            } else if (property === 'x') {
                selectedObject.x(value);
            } else if (property === 'y') {
                selectedObject.y(value);
            }

            layer.draw();
            saveState();
        });

        $('#objRotation').on('input', function() {
            if (!selectedObject) return;
            selectedObject.rotation(parseFloat($(this).val()));
            layer.draw();
        });

        $('#objOpacity').on('input', function() {
            if (!selectedObject) return;
            selectedObject.opacity(parseFloat($(this).val()));
            layer.draw();
        });

        // Object selection
        function selectObject(obj) {
            // Remove existing transformers
            layer.find('Transformer').destroy();

            selectedObject = obj;

            if (obj && obj.name() !== 'background') {
                // Add transformer
                const transformer = new Konva.Transformer({
                    nodes: [obj],
                    keepRatio: false,
                    enabledAnchors: ['top-left', 'top-right', 'bottom-left', 'bottom-right']
                });
                layer.add(transformer);
            }

            layer.draw();
            updatePropertiesPanel();
        }

        // Actions
        function handleAction(action) {
            switch (action) {
                case 'clear':
                    if (confirm('Are you sure you want to clear the canvas?')) {
                        layer.destroyChildren();
                        initCanvas();
                    }
                    break;

                case 'undo':
                    undo();
                    break;

                case 'redo':
                    redo();
                    break;
            }
        }

        // History management
        function saveState() {
            historyStep++;
            if (historyStep < history.length) {
                history.length = historyStep;
            }
            history.push(stage.toJSON());
        }

        function undo() {
            if (historyStep > 0) {
                historyStep--;
                const state = history[historyStep];
                stage.destroy();
                stage = Konva.Node.create(state, 'designCanvas');
                layer = stage.getLayers()[0];
                selectObject(null);
            }
        }

        function redo() {
            if (historyStep < history.length - 1) {
                historyStep++;
                const state = history[historyStep];
                stage.destroy();
                stage = Konva.Node.create(state, 'designCanvas');
                layer = stage.getLayers()[0];
                selectObject(null);
            }
        }

        // Update layers list
        function updateLayers() {
            const layersList = $('#layersList');
            layersList.empty();

            const children = layer.getChildren();
            children.each(function(child, index) {
                if (child.getClassName() === 'Transformer') return;

                const layerItem = $(`
                    <div class="layer-item p-2 border rounded mb-2" data-id="${child._id}">
                        <small>${child.name() || child.getClassName()}</small>
                        <div class="float-end">
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteLayer('${child._id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `);

                layerItem.click(function() {
                    selectObject(child);
                });

                layersList.append(layerItem);
            });
        }

        // Delete layer
        function deleteLayer(id) {
            const obj = stage.findOne('#' + id);
            if (obj) {
                obj.destroy();
                layer.draw();
                saveState();
                updateLayers();
                selectObject(null);
            }
        }

        // Save template
        $('#saveTemplate').click(function() {
            const templateData = stage.toJSON();
            $('#templateDataInput').val(templateData);
            $('#saveTemplateModal').modal('show');
        });

        // Export template
        $('#exportTemplate').click(function() {
            const dataURL = stage.toDataURL({
                mimeType: 'image/png',
                quality: 1.0,
                pixelRatio: 2
            });

            const link = document.createElement('a');
            link.download = 'festival-template.png';
            link.href = dataURL;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        });

        // Zoom functionality
        let zoomLevel = 1;

        $('#zoomIn').click(function() {
            zoomLevel = Math.min(zoomLevel + 0.1, 3);
            updateZoom();
        });

        $('#zoomOut').click(function() {
            zoomLevel = Math.max(zoomLevel - 0.1, 0.1);
            updateZoom();
        });

        function updateZoom() {
            stage.scale({ x: zoomLevel, y: zoomLevel });
            stage.draw();
            $('#zoomLevel').text(Math.round(zoomLevel * 100) + '%');
        }

        // Load template
        function loadTemplate(templateId) {
            // This would load template data from the server
            console.log('Loading template:', templateId);
        }

        // Delete template
        function deleteTemplate(templateId) {
            if (confirm('Are you sure you want to delete this template?')) {
                // This would delete the template from the server
                console.log('Deleting template:', templateId);
            }
        }

        // Initialize on page load
        $(document).ready(function() {
            // Replace canvas element with div for Konva
            $('#designCanvas').replaceWith('<div id="designCanvas"></div>');
            initCanvas();
            updateLayers();
        });
    </script>
