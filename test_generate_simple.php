<?php
// Simple test version of generate_image.php
header('Content-Type: application/json');

try {
    // Test 1: Basic PHP functionality
    if (!function_exists('json_encode')) {
        throw new Exception('JSON functions not available');
    }
    
    // Test 2: Check if it's POST request
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        echo json_encode(['success' => false, 'message' => 'Method: ' . $_SERVER['REQUEST_METHOD']]);
        exit;
    }
    
    // Test 3: Check POST data
    if (empty($_POST)) {
        echo json_encode(['success' => false, 'message' => 'No POST data received']);
        exit;
    }
    
    // Test 4: Try to include config files
    if (!file_exists('config/config.php')) {
        echo json_encode(['success' => false, 'message' => 'Config file not found']);
        exit;
    }
    
    require_once 'config/config.php';
    
    if (!file_exists('config/database.php')) {
        echo json_encode(['success' => false, 'message' => 'Database config not found']);
        exit;
    }
    
    require_once 'config/database.php';
    
    // Test 5: Check database connection
    if (!isset($pdo)) {
        echo json_encode(['success' => false, 'message' => 'Database connection not established']);
        exit;
    }
    
    // Test 6: Try a simple database query
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM festivals");
    $result = $stmt->fetch();
    
    // Test 7: Check session functions
    require_once 'config/session.php';
    
    if (!function_exists('isLoggedIn')) {
        echo json_encode(['success' => false, 'message' => 'Session functions not loaded']);
        exit;
    }
    
    // Test 8: Check if user is logged in
    if (!isLoggedIn()) {
        echo json_encode(['success' => false, 'message' => 'User not logged in. Session: ' . json_encode($_SESSION)]);
        exit;
    }
    
    // Test 9: Get user ID
    $user_id = getCurrentUserId();
    if (!$user_id) {
        echo json_encode(['success' => false, 'message' => 'No user ID in session']);
        exit;
    }
    
    // Test 10: Check includes
    require_once 'includes/functions.php';
    
    if (!function_exists('sanitize_input')) {
        echo json_encode(['success' => false, 'message' => 'Functions not loaded']);
        exit;
    }
    
    // Test 11: Validate CSRF token
    if (!function_exists('validateCSRFToken')) {
        echo json_encode(['success' => false, 'message' => 'CSRF functions not available']);
        exit;
    }
    
    $csrf_token = $_POST['csrf_token'] ?? '';
    if (!validateCSRFToken($csrf_token)) {
        echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
        exit;
    }
    
    // Test 12: Get and validate input
    $festival_id = intval($_POST['festival_id'] ?? 0);
    $template_id = intval($_POST['template_id'] ?? 0);
    $message = sanitize_input($_POST['message'] ?? '');
    
    if (!$festival_id || !$template_id) {
        echo json_encode(['success' => false, 'message' => 'Missing festival or template ID']);
        exit;
    }
    
    // Test 13: Check template exists in database
    $stmt = $pdo->prepare("SELECT * FROM templates WHERE id = ? AND festival_id = ? AND is_active = 1");
    $stmt->execute([$template_id, $festival_id]);
    $template = $stmt->fetch();
    
    if (!$template) {
        echo json_encode(['success' => false, 'message' => 'Template not found in database']);
        exit;
    }
    
    // Test 14: Check template file exists
    $template_path =  'uploads/' . $template['image_path'];
    if (!file_exists($template_path)) {
        echo json_encode(['success' => false, 'message' => 'Template file not found: ' . $template['image_path']]);
        exit;
    }
    
    // Test 15: Check GD extension
    if (!extension_loaded('gd')) {
        echo json_encode(['success' => false, 'message' => 'GD extension not loaded']);
        exit;
    }
    
    // Test 16: Check output directory
    $output_dir = ROOT_PATH . '/' . UPLOAD_DIR_GENERATED;
    if (!is_dir($output_dir)) {
        if (!mkdir($output_dir, 0755, true)) {
            echo json_encode(['success' => false, 'message' => 'Cannot create output directory']);
            exit;
        }
    }
    
    if (!is_writable($output_dir)) {
        echo json_encode(['success' => false, 'message' => 'Output directory not writable']);
        exit;
    }
    
    // If we get here, everything should work
    echo json_encode([
        'success' => true, 
        'message' => 'All tests passed!',
        'data' => [
            'user_id' => $user_id,
            'festival_id' => $festival_id,
            'template_id' => $template_id,
            'template_path' => $template['image_path'],
            'message' => $message,
            'festivals_count' => $result['count']
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Exception: ' . $e->getMessage()]);
} catch (Error $e) {
    echo json_encode(['success' => false, 'message' => 'Fatal Error: ' . $e->getMessage()]);
}
?>
