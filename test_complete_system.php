<?php
session_start();
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

// Create a test session if not logged in
if (!isLoggedIn()) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'Test User';
    $_SESSION['user_email'] = '<EMAIL>';
}

echo "<h1>🎉 FestivalKit - Complete System Test</h1>";

// Test 1: System Components
echo "<h2>1. System Components</h2>";
$all_good = true;

// GD Extension
if (extension_loaded('gd')) {
    echo "✅ GD Extension loaded<br>";
} else {
    echo "❌ GD Extension missing<br>";
    $all_good = false;
}

// Database
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM festivals");
    $result = $stmt->fetch();
    echo "✅ Database connected ({$result['count']} festivals)<br>";
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
    $all_good = false;
}

// Upload directories
$dirs = ['uploads/logos', 'uploads/templates', 'uploads/generated'];
foreach ($dirs as $dir) {
    if (is_dir($dir) && is_writable($dir)) {
        echo "✅ Directory $dir is writable<br>";
    } else {
        echo "❌ Directory $dir issue<br>";
        $all_good = false;
    }
}

// Test 2: Template Files
echo "<h2>2. Template Files</h2>";
try {
    $stmt = $pdo->query("SELECT * FROM templates WHERE is_active = 1 LIMIT 3");
    $templates = $stmt->fetchAll();
    
    foreach ($templates as $template) {
        if (file_exists($template['image_path'])) {
            echo "✅ Template '{$template['name']}' exists<br>";
        } else {
            echo "❌ Template '{$template['name']}' missing: {$template['image_path']}<br>";
            $all_good = false;
        }
    }
} catch (Exception $e) {
    echo "❌ Template check failed: " . $e->getMessage() . "<br>";
    $all_good = false;
}

// Test 3: Image Generation
echo "<h2>3. Image Generation Test</h2>";
if ($all_good && !empty($templates)) {
    $template = $templates[0];
    $output_path = ROOT_PATH . '/uploads/generated/system_test_' . time() . '.jpg';
    $test_message = "System Test - " . date('H:i:s');
    
    $result = generateFestivalImage($template['image_path'], null, $test_message, $output_path);
    
    if ($result['success']) {
        echo "✅ Image generation successful<br>";
        if (file_exists($output_path)) {
            $size = getimagesize($output_path);
            echo "✅ Generated file: " . basename($output_path) . " ({$size[0]}x{$size[1]})<br>";
            
            // Show preview
            $preview_url = getUploadUrl('generated/' . basename($output_path));
            echo "<img src='$preview_url' style='max-width: 300px; border: 2px solid #28a745; margin: 10px 0;'><br>";
            echo "✅ Preview displayed successfully<br>";
            
            // Clean up
            unlink($output_path);
            echo "✅ Test file cleaned up<br>";
        } else {
            echo "❌ Generated file not found<br>";
            $all_good = false;
        }
    } else {
        echo "❌ Image generation failed: " . $result['message'] . "<br>";
        $all_good = false;
    }
}

// Final Result
echo "<h2>🎯 Final Result</h2>";
if ($all_good) {
    echo "<div style='padding: 20px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; color: #155724;'>";
    echo "<h3>🎉 SUCCESS! FestivalKit is Ready</h3>";
    echo "<p>All systems are working correctly. You can now:</p>";
    echo "<ul>";
    echo "<li>✅ Use the <a href='free_generator.php'>Free Generator</a></li>";
    echo "<li>✅ Use the <a href='kit_generator.php'>Kit Generator</a></li>";
    echo "<li>✅ Generate images with logos and text</li>";
    echo "<li>✅ Download generated images</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='padding: 20px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; color: #721c24;'>";
    echo "<h3>❌ Issues Found</h3>";
    echo "<p>Please fix the issues above before using the system.</p>";
    echo "</div>";
}

echo "<h2>🔗 Quick Links</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='free_generator.php' style='display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>Free Generator</a>";
echo "<a href='kit_generator.php' style='display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>Kit Generator</a>";
echo "<a href='index.php' style='display: inline-block; padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>Homepage</a>";
echo "</div>";

echo "<h2>📋 System Information</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;'>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "GD Version: " . (extension_loaded('gd') ? gd_info()['GD Version'] : 'Not loaded') . "<br>";
echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "Upload Max: " . ini_get('upload_max_filesize') . "<br>";
echo "Debug Mode: " . (DEBUG_MODE ? 'Enabled' : 'Disabled') . "<br>";
echo "</div>";
?>

<style>
body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2 { color: #333; }
h1 { border-bottom: 3px solid #007bff; padding-bottom: 10px; }
h2 { border-bottom: 1px solid #dee2e6; padding-bottom: 5px; margin-top: 30px; }
</style>
