<?php
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../config/session.php';
require_once '../includes/functions.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'Please log in.']);
    exit;
}

// Get festival ID
$festival_id = intval($_GET['festival_id'] ?? 0);

if (!$festival_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid festival ID.']);
    exit;
}

try {
    // Get templates for the festival
    $templates = getTemplatesByFestival($pdo, $festival_id);
    
    // Format templates for response
    $formatted_templates = [];
    foreach ($templates as $template) {
        $formatted_templates[] = [
            'id' => $template['id'],
            'name' => $template['name'],
            'image_url' => getUploadUrl($template['image_path'])
        ];
    }
    
    echo json_encode([
        'success' => true,
        'templates' => $formatted_templates
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Failed to load templates.'
    ]);
}
?>
