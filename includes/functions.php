<?php
// Core utility functions for FestivalKit

/**
 * Sanitize input data
 */
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Validate email format
 */
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * Hash password
 */
function hash_password($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password
 */
function verify_password($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Generate random string
 */
function generate_random_string($length = 10) {
    return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
}

/**
 * Get featured festivals
 */
function getFeaturedFestivals($pdo, $limit = 6) {
    // Ensure limit is an integer for security
    $limit = intval($limit);
    $stmt = $pdo->prepare("SELECT * FROM festivals WHERE is_featured = 1 ORDER BY date ASC LIMIT " . $limit);
    $stmt->execute();
    return $stmt->fetchAll();
}

/**
 * Get all festivals
 */
function getAllFestivals($pdo) {
    $stmt = $pdo->query("SELECT * FROM festivals ORDER BY date ASC");
    return $stmt->fetchAll();
}

/**
 * Get templates for a festival
 */
function getTemplatesByFestival($pdo, $festival_id) {
    $stmt = $pdo->prepare("SELECT * FROM templates WHERE festival_id = ? AND is_active = 1");
    $stmt->execute([$festival_id]);
    return $stmt->fetchAll();
}

/**
 * Get user by ID
 */
function getUserById($pdo, $user_id) {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    return $stmt->fetch();
}

/**
 * Get user by email
 */
function getUserByEmail($pdo, $email) {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$email]);
    return $stmt->fetch();
}

/**
 * Create new user
 */
function createUser($pdo, $name, $email, $password) {
    $hashed_password = hash_password($password);
    $stmt = $pdo->prepare("INSERT INTO users (name, email, password) VALUES (?, ?, ?)");
    return $stmt->execute([$name, $email, $hashed_password]);
}

/**
 * Upload file with validation
 */
function uploadFile($file, $upload_dir, $allowed_types = ['jpg', 'jpeg', 'png'], $max_size = 5242880) {
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'Upload error occurred'];
    }
    
    if ($file['size'] > $max_size) {
        return ['success' => false, 'message' => 'File too large'];
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'message' => 'Invalid file type'];
    }
    
    $filename = uniqid() . '.' . $file_extension;
    $filepath = $upload_dir . '/' . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'filename' => $filename, 'filepath' => $filepath];
    }
    
    return ['success' => false, 'message' => 'Failed to move uploaded file'];
}

/**
 * Generate festival image with logo and text overlay
 */
function generateFestivalImage($template_path, $logo_path, $message, $output_path) {
    // Check if GD extension is loaded
    if (!extension_loaded('gd')) {
        return ['success' => false, 'message' => 'GD extension not available'];
    }

    // Check if template file exists - handle both absolute and relative paths
    $full_template_path = $template_path;
    if (!file_exists($full_template_path) && substr($template_path, 0, 1) !== '/') {
        // Try with ROOT_PATH if it's a relative path
        $full_template_path = ROOT_PATH . '/' . $template_path;
    }

    if (!file_exists($full_template_path)) {
        return ['success' => false, 'message' => 'Template file not found: ' . $template_path];
    }

    // Use the full path for processing
    $template_path = $full_template_path;

    // Load template image
    $template_info = getimagesize($template_path);
    if (!$template_info) {
        return ['success' => false, 'message' => 'Invalid template image: ' . $template_path];
    }
    
    switch ($template_info[2]) {
        case IMAGETYPE_JPEG:
            $template = imagecreatefromjpeg($template_path);
            break;
        case IMAGETYPE_PNG:
            $template = imagecreatefrompng($template_path);
            break;
        default:
            return ['success' => false, 'message' => 'Unsupported template format'];
    }
    
    if (!$template) {
        return ['success' => false, 'message' => 'Failed to load template'];
    }
    
    $template_width = imagesx($template);
    $template_height = imagesy($template);
    
    // Load and resize logo if provided
    if ($logo_path && file_exists($logo_path)) {
        $logo_info = getimagesize($logo_path);
        if ($logo_info) {
            switch ($logo_info[2]) {
                case IMAGETYPE_JPEG:
                    $logo = imagecreatefromjpeg($logo_path);
                    break;
                case IMAGETYPE_PNG:
                    $logo = imagecreatefrompng($logo_path);
                    break;
                default:
                    $logo = null;
            }
            
            if ($logo) {
                $logo_width = imagesx($logo);
                $logo_height = imagesy($logo);
                
                // Resize logo to fit template (max 20% of template width)
                $max_logo_width = $template_width * 0.2;
                $max_logo_height = $template_height * 0.2;
                
                if ($logo_width > $max_logo_width || $logo_height > $max_logo_height) {
                    $ratio = min($max_logo_width / $logo_width, $max_logo_height / $logo_height);
                    $new_logo_width = $logo_width * $ratio;
                    $new_logo_height = $logo_height * $ratio;
                    
                    $resized_logo = imagecreatetruecolor($new_logo_width, $new_logo_height);
                    imagealphablending($resized_logo, false);
                    imagesavealpha($resized_logo, true);
                    imagecopyresampled($resized_logo, $logo, 0, 0, 0, 0, $new_logo_width, $new_logo_height, $logo_width, $logo_height);
                    
                    // Position logo (top-right corner)
                    $logo_x = $template_width - $new_logo_width - 20;
                    $logo_y = 20;
                    imagecopy($template, $resized_logo, $logo_x, $logo_y, 0, 0, $new_logo_width, $new_logo_height);
                    
                    imagedestroy($resized_logo);
                } else {
                    // Position original logo
                    $logo_x = $template_width - $logo_width - 20;
                    $logo_y = 20;
                    imagecopy($template, $logo, $logo_x, $logo_y, 0, 0, $logo_width, $logo_height);
                }
                
                imagedestroy($logo);
            }
        }
    }
    
    // Add text message if provided
    if (!empty($message)) {
        $text_color = imagecolorallocate($template, 255, 255, 255); // White text
        $font_size = 20;
        
        // Try to use a TTF font, fallback to built-in font
        $font_path = __DIR__ . '/../assets/fonts/arial.ttf';
        if (file_exists($font_path)) {
            // Calculate text position (center bottom)
            $text_box = imagettfbbox($font_size, 0, $font_path, $message);
            $text_width = $text_box[4] - $text_box[0];
            $text_height = $text_box[1] - $text_box[7];
            
            $text_x = ($template_width - $text_width) / 2;
            $text_y = $template_height - 50;
            
            imagettftext($template, $font_size, 0, $text_x, $text_y, $text_color, $font_path, $message);
        } else {
            // Use built-in font
            $text_x = ($template_width - strlen($message) * 10) / 2;
            $text_y = $template_height - 30;
            imagestring($template, 5, $text_x, $text_y, $message, $text_color);
        }
    }
    
    // Save the generated image
    $result = imagejpeg($template, $output_path, 90);
    imagedestroy($template);
    
    if ($result) {
        return ['success' => true, 'output_path' => $output_path];
    } else {
        return ['success' => false, 'message' => 'Failed to save generated image'];
    }
}

/**
 * Create ZIP file with multiple images
 */
function createZipFile($files, $zip_path) {
    $zip = new ZipArchive();
    if ($zip->open($zip_path, ZipArchive::CREATE) !== TRUE) {
        return false;
    }
    
    foreach ($files as $file) {
        if (file_exists($file['path'])) {
            $zip->addFile($file['path'], $file['name']);
        }
    }
    
    $zip->close();
    return file_exists($zip_path);
}

/**
 * Get setting value
 */
function getSetting($pdo, $key, $default = null) {
    $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
    $stmt->execute([$key]);
    $result = $stmt->fetch();
    return $result ? $result['setting_value'] : $default;
}

/**
 * Update setting value
 */
function updateSetting($pdo, $key, $value) {
    $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
    return $stmt->execute([$key, $value, $value]);
}

/**
 * Format currency
 */
function formatCurrency($amount) {
    return '$' . number_format($amount, 2);
}

/**
 * Generate AI design using OpenAI DALL-E API (placeholder implementation)
 */
function generateAIDesign($festival_name, $design_style, $color_scheme, $theme) {
    // For now, this is a placeholder that creates a sample design
    // In production, you would integrate with OpenAI DALL-E API or similar service

    try {
        // Create a sample design using GD library
        $width = 800;
        $height = 600;

        $image = imagecreatetruecolor($width, $height);

        // Set colors based on color scheme
        switch ($color_scheme) {
            case 'warm':
                $bg_color = imagecolorallocate($image, 255, 107, 107);
                $accent_color = imagecolorallocate($image, 254, 202, 87);
                break;
            case 'cool':
                $bg_color = imagecolorallocate($image, 84, 160, 255);
                $accent_color = imagecolorallocate($image, 95, 39, 205);
                break;
            case 'gold':
                $bg_color = imagecolorallocate($image, 243, 156, 18);
                $accent_color = imagecolorallocate($image, 211, 84, 0);
                break;
            default:
                $bg_color = imagecolorallocate($image, 102, 126, 234);
                $accent_color = imagecolorallocate($image, 118, 75, 162);
        }

        $white = imagecolorallocate($image, 255, 255, 255);
        $black = imagecolorallocate($image, 0, 0, 0);

        // Create gradient background
        for ($i = 0; $i < $height; $i++) {
            $alpha = $i / $height;
            $r = (1 - $alpha) * (($bg_color >> 16) & 0xFF) + $alpha * (($accent_color >> 16) & 0xFF);
            $g = (1 - $alpha) * (($bg_color >> 8) & 0xFF) + $alpha * (($accent_color >> 8) & 0xFF);
            $b = (1 - $alpha) * ($bg_color & 0xFF) + $alpha * ($accent_color & 0xFF);

            $line_color = imagecolorallocate($image, $r, $g, $b);
            imageline($image, 0, $i, $width, $i, $line_color);
        }

        // Add decorative elements based on style
        if ($design_style === 'modern') {
            // Add geometric shapes
            imagefilledellipse($image, $width * 0.8, $height * 0.2, 100, 100, $white);
            imagefilledrectangle($image, 50, $height - 150, 150, $height - 50, $white);
        } elseif ($design_style === 'traditional') {
            // Add ornate patterns (simplified)
            for ($i = 0; $i < 5; $i++) {
                imagefilledellipse($image, 100 + $i * 150, 100, 50, 50, $white);
            }
        } elseif ($design_style === 'elegant') {
            // Add elegant borders
            imagerectangle($image, 20, 20, $width - 20, $height - 20, $white);
            imagerectangle($image, 40, 40, $width - 40, $height - 40, $white);
        }

        // Add festival name text
        $font_size = 36;
        $text_box = imagettfbbox($font_size, 0, __DIR__ . '/../assets/fonts/arial.ttf', $festival_name);
        $text_width = $text_box[4] - $text_box[0];
        $text_x = ($width - $text_width) / 2;
        $text_y = $height / 2;

        // Add text shadow
        imagettftext($image, $font_size, 0, $text_x + 2, $text_y + 2, $black, __DIR__ . '/../assets/fonts/arial.ttf', $festival_name);
        imagettftext($image, $font_size, 0, $text_x, $text_y, $white, __DIR__ . '/../assets/fonts/arial.ttf', $festival_name);

        // Add theme elements text if provided
        if (!empty($theme)) {
            $theme_text = "Theme: " . $theme;
            $small_font = 16;
            $theme_box = imagettfbbox($small_font, 0, __DIR__ . '/../assets/fonts/arial.ttf', $theme_text);
            $theme_width = $theme_box[4] - $theme_box[0];
            $theme_x = ($width - $theme_width) / 2;
            $theme_y = $text_y + 60;

            imagettftext($image, $small_font, 0, $theme_x + 1, $theme_y + 1, $black, __DIR__ . '/../assets/fonts/arial.ttf', $theme_text);
            imagettftext($image, $small_font, 0, $theme_x, $theme_y, $white, __DIR__ . '/../assets/fonts/arial.ttf', $theme_text);
        }

        // Save the generated image
        $filename = 'ai_generated_' . time() . '_' . rand(1000, 9999) . '.jpg';
        $filepath = ROOT_PATH . '/uploads/generated/' . $filename;

        if (imagejpeg($image, $filepath, 90)) {
            imagedestroy($image);

            return [
                'success' => true,
                'image_url' => 'uploads/generated/' . $filename,
                'filename' => $filename
            ];
        } else {
            imagedestroy($image);
            return [
                'success' => false,
                'message' => 'Failed to save AI generated image'
            ];
        }

    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'AI generation failed: ' . $e->getMessage()
        ];
    }
}

/**
 * Check if user is admin
 */
function isAdmin() {
    return isset($_SESSION['admin_id']);
}

/**
 * Redirect with message
 */
function redirect($url, $message = null, $type = 'info') {
    if ($message) {
        $_SESSION['flash_message'] = $message;
        $_SESSION['flash_type'] = $type;
    }
    header("Location: $url");
    exit();
}

/**
 * Display flash message
 */
function displayFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        
        $alert_class = 'alert-info';
        switch ($type) {
            case 'success':
                $alert_class = 'alert-success';
                break;
            case 'error':
                $alert_class = 'alert-danger';
                break;
            case 'warning':
                $alert_class = 'alert-warning';
                break;
        }
        
        echo "<div class='alert $alert_class alert-dismissible fade show' role='alert'>
                $message
                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
              </div>";
    }
}
?>
