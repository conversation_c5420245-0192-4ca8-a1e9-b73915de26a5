<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

// Set JSON response header
header('Content-Type: application/json');

// Require user login
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'Please log in to generate images.']);
    exit;
}

$user_id = getCurrentUserId();

// Validate user ID
if (!$user_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid user session. Please log in again.']);
    exit;
}

// Verify user exists in database
try {
    $stmt = $pdo->prepare("SELECT id, name, email, package_type FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    if (!$user) {
        // Log the issue for debugging
        error_log("User not found in database. User ID: " . $user_id . ", Session: " . print_r($_SESSION, true));
        echo json_encode(['success' => false, 'message' => 'User not found. Please log in again.']);
        exit;
    }
} catch (PDOException $e) {
    error_log("Database error in generate_image.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error. Please try again.']);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
    exit;
}

// Validate CSRF token
if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
    echo json_encode(['success' => false, 'message' => 'Invalid request token.']);
    exit;
}

try {
    // Get and validate input
    $festival_id = intval($_POST['festival_id'] ?? 0);
    $template_id = intval($_POST['template_id'] ?? 0);
    $message = sanitize_input($_POST['message'] ?? '');
    
    // Validate required fields
    if (!$festival_id || !$template_id) {
        throw new Exception('Please select a festival and template.');
    }
    
    // Validate message length
    if (strlen($message) > MAX_MESSAGE_LENGTH) {
        throw new Exception('Message is too long. Maximum ' . MAX_MESSAGE_LENGTH . ' characters allowed.');
    }
    
    // Get template information
    $stmt = $pdo->prepare("SELECT t.*, f.name as festival_name FROM templates t 
                          JOIN festivals f ON t.festival_id = f.id 
                          WHERE t.id = ? AND t.festival_id = ? AND t.is_active = 1");
    $stmt->execute([$template_id, $festival_id]);
    $template = $stmt->fetch();
    
    if (!$template) {
        throw new Exception('Invalid template selection.');
    }
    
    // Check if template file exists
    $template_path = ROOT_PATH . '/' . $template['image_path'];
    if (!file_exists($template_path)) {
        throw new Exception('Template image not found.');
    }
    
    // Handle logo upload if provided
    $logo_path = null;
    if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
        $upload_result = uploadFile($_FILES['logo'], ROOT_PATH . '/' . UPLOAD_DIR_LOGOS);
        if (!$upload_result['success']) {
            throw new Exception($upload_result['message']);
        }
        $logo_path = $upload_result['filepath'];
    }
    
    // Generate unique filename for output
    $output_filename = 'generated_' . $user_id . '_' . time() . '.jpg';
    $output_path = ROOT_PATH . '/' . UPLOAD_DIR_GENERATED . $output_filename;
    
    // Debug logging
    error_log("Generating image with:");
    error_log("Template path: " . $template_path);
    error_log("Logo path: " . ($logo_path ?? 'none'));
    error_log("Message: " . $message);
    error_log("Output path: " . $output_path);

    // Check if output directory exists and is writable
    $output_dir = dirname($output_path);
    if (!is_dir($output_dir)) {
        if (!mkdir($output_dir, 0755, true)) {
            throw new Exception('Failed to create output directory.');
        }
    }

    if (!is_writable($output_dir)) {
        throw new Exception('Output directory is not writable.');
    }

    // Generate the image
    $generation_result = generateFestivalImage($template_path, $logo_path, $message, $output_path);

    if (!$generation_result['success']) {
        error_log("Image generation failed: " . $generation_result['message']);
        throw new Exception($generation_result['message']);
    }

    // Verify the output file was created
    if (!file_exists($output_path)) {
        throw new Exception('Generated image file not found.');
    }

    error_log("Image generated successfully: " . $output_path);
    
    // Clean up temporary logo file if uploaded
    if ($logo_path && file_exists($logo_path)) {
        // Keep the logo for potential reuse, or delete if you prefer
        // unlink($logo_path);
    }
    
    // Return success response
    $response = [
        'success' => true,
        'message' => 'Image generated successfully!',
        'preview_url' => getUploadUrl(UPLOAD_DIR_GENERATED . $output_filename),
        'download_url' => 'download.php?file=' . urlencode($output_filename) . '&type=generated'
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
