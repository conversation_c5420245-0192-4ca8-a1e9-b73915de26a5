# 🎉 FestivalKit - Professional Festival Image Generator

A complete, production-ready web application for generating beautiful festival marketing images with custom branding. Perfect for businesses, marketers, and social media managers who need professional festival content quickly.

## ✨ Key Features

### 🎨 User Features
- **Professional Homepage** with featured festivals and smooth user onboarding
- **Secure User Authentication** with registration, login, and session management
- **Free Image Generator** - Create single festival images with logo overlay and custom messages
- **Premium Kit Generator** - Generate complete festival image sets for all occasions
- **Auto Social Media Posting** - Schedule and automate social media posts (Premium feature)
- **Responsive Design** - Works perfectly on desktop, tablet, and mobile devices

### 🛠 Admin Features
- **Comprehensive Admin Dashboard** with real-time statistics and analytics
- **Festival Management** - Complete CRUD operations for festivals and templates
- **User Management** - Monitor user activity, manage subscriptions, and view detailed profiles
- **Payment Processing** - Track transactions, export reports, and manage billing
- **System Settings** - Configure pricing, features, and application behavior
- **Template Management** - Upload, organize, and manage festival templates

## 🚀 Technology Stack
- **Backend**: PHP 7.4+ with modern practices and security features
- **Database**: MySQL 5.7+ with optimized schema and indexing
- **Frontend**: HTML5, CSS3, Bootstrap 5 with custom responsive design
- **JavaScript**: jQuery with AJAX for dynamic interactions
- **Image Processing**: PHP GD Library for professional image manipulation
- **File Handling**: ZIP creation, secure uploads, and download management
- **Security**: CSRF protection, SQL injection prevention, secure file handling

## 📦 Quick Installation

### Prerequisites
- **PHP 7.4+** with GD, ZIP, and PDO extensions
- **MySQL 5.7+** or MariaDB 10.2+
- **Web Server** (Apache/Nginx) with mod_rewrite
- **SSL Certificate** (recommended for production)

### 🚀 One-Click Setup

1. **Download & Extract**
   ```bash
   # Download the project files to your web directory
   cd /var/www/html/
   # Extract FestivalKit files here
   ```

2. **Database Setup**
   ```bash
   # Create database and import schema
   mysql -u root -p -e "CREATE DATABASE festivalkit;"
   mysql -u root -p festivalkit < database_schema.sql
   ```

3. **Configuration**
   ```bash
   # Update database credentials
   nano config/database.php

   # Set secure permissions
   chmod 755 uploads/ uploads/*/
   chmod 600 config/database.php
   ```

4. **Generate Sample Content**
   ```bash
   # Create sample template images
   php create_sample_images.php

   # Test installation
   php test_installation.php
   ```

5. **Access Your Site**
   - **Homepage**: `https://yourdomain.com/`
   - **Admin Panel**: `https://yourdomain.com/admin/`
   - **Default Admin**: `admin` / `admin123`

## Directory Structure

```
festiveCreatives/
├── admin/                  # Admin panel files
│   ├── includes/          # Admin includes
│   └── ...
├── assets/                # Static assets
│   ├── css/              # Stylesheets
│   ├── js/               # JavaScript files
│   └── images/           # Static images
├── config/               # Configuration files
├── includes/             # PHP includes and functions
├── uploads/              # Upload directories
│   ├── logos/           # User uploaded logos
│   ├── templates/       # Festival templates
│   └── generated/       # Generated images
├── index.php            # Homepage
├── login.php            # User login
├── register.php         # User registration
├── dashboard.php        # User dashboard
└── database_schema.sql  # Database schema
```

## Key Features Implementation

### Image Generation
- Uses PHP GD library for image manipulation
- Supports PNG/JPG logo uploads
- Text overlay with custom messages
- Automatic logo resizing and positioning

### Payment Integration
- Mock PayPal/Stripe integration for demonstration
- Payment logging and status tracking
- Automatic kit generation after successful payment

### Admin Panel
- Complete CRUD operations for festivals and templates
- User management with activity tracking
- Payment history with CSV export
- Settings management

### Security Features
- Password hashing with PHP's password_hash()
- SQL injection prevention with prepared statements
- File upload validation and sanitization
- Session management for authentication

## Usage

### For Users
1. Register/Login to access features
2. **Free Generator**: Select template, upload logo, add message, generate image
3. **Paid Kit**: Upload business details, pay $10, download complete festival kit
4. **Premium**: Connect social accounts for auto-posting (demo mode)

### For Admins
1. Login at `/admin/` with admin credentials
2. Manage festivals and upload templates
3. Monitor user activity and payments
4. Export reports and configure settings

## Customization

### Adding New Festivals
1. Login to admin panel
2. Go to Festival Management
3. Add festival with name, date, description
4. Upload template images for the festival

### Modifying Pricing
1. Admin panel → Settings
2. Update "Kit Price" setting
3. Changes apply immediately

### Template Requirements
- Format: JPG or PNG
- Recommended size: 1200x800px
- Ensure good contrast for text overlay
- Leave space for logo placement (top-right recommended)

## Development Notes

### Database Schema
- **users**: User accounts and package types
- **festivals**: Festival information and dates
- **templates**: Template images linked to festivals
- **user_uploads**: User logos and business information
- **kit_downloads**: Generated kit tracking
- **payments**: Payment transaction logs
- **scheduled_posts**: Social media post scheduling
- **admin_users**: Admin account management
- **settings**: Application configuration

### File Upload Limits
- Maximum logo size: 5MB
- Supported formats: JPG, JPEG, PNG
- Automatic image validation and processing

### Image Generation Process
1. Load festival template
2. Resize and position user logo
3. Add text overlay with custom message
4. Save generated image
5. Provide download link

## Troubleshooting

### Common Issues
1. **GD Extension Missing**: Install php-gd extension
2. **Upload Permissions**: Check directory permissions (755)
3. **Database Connection**: Verify credentials in config/database.php
4. **Image Generation Fails**: Check GD library and file permissions

### Error Logs
- Check PHP error logs for detailed error information
- Enable error reporting in development environment

## License
This project is for educational/demonstration purposes.

## Support
For issues and questions, please check the code comments and documentation within the PHP files.
