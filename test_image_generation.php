<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h1>FestivalKit System Test</h1>";

// Test 1: Check GD extension
echo "<h2>1. GD Extension Test</h2>";
if (extension_loaded('gd')) {
    echo "✅ GD extension is loaded<br>";
    $gd_info = gd_info();
    echo "GD Version: " . $gd_info['GD Version'] . "<br>";
    echo "JPEG Support: " . ($gd_info['JPEG Support'] ? 'Yes' : 'No') . "<br>";
    echo "PNG Support: " . ($gd_info['PNG Support'] ? 'Yes' : 'No') . "<br>";
} else {
    echo "❌ GD extension is NOT loaded<br>";
}

// Test 2: Check database connection
echo "<h2>2. Database Connection Test</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM festivals");
    $result = $stmt->fetch();
    echo "✅ Database connection successful<br>";
    echo "Festivals in database: " . $result['count'] . "<br>";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
}

// Test 3: Check upload directories
echo "<h2>3. Upload Directories Test</h2>";
$dirs = [
    'uploads/logos' => ROOT_PATH . '/uploads/logos',
    'uploads/templates' => ROOT_PATH . '/uploads/templates',
    'uploads/generated' => ROOT_PATH . '/uploads/generated'
];

foreach ($dirs as $name => $path) {
    if (is_dir($path)) {
        if (is_writable($path)) {
            echo "✅ $name directory exists and is writable<br>";
        } else {
            echo "⚠️ $name directory exists but is NOT writable<br>";
        }
    } else {
        echo "❌ $name directory does NOT exist<br>";
    }
}

// Test 4: Check template files
echo "<h2>4. Template Files Test</h2>";
try {
    $stmt = $pdo->query("SELECT * FROM templates WHERE is_active = 1 LIMIT 5");
    $templates = $stmt->fetchAll();
    
    foreach ($templates as $template) {
        $template_path = ROOT_PATH . '/' . $template['image_path'];
        if (file_exists($template_path)) {
            $size = getimagesize($template_path);
            if ($size) {
                echo "✅ Template '{$template['name']}' exists and is valid ({$size[0]}x{$size[1]})<br>";
            } else {
                echo "❌ Template '{$template['name']}' exists but is not a valid image<br>";
            }
        } else {
            echo "❌ Template '{$template['name']}' file missing: {$template_path}<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Template check failed: " . $e->getMessage() . "<br>";
}

// Test 5: Test image generation
echo "<h2>5. Image Generation Test</h2>";
try {
    // Get first available template
    $stmt = $pdo->query("SELECT * FROM templates WHERE is_active = 1 LIMIT 1");
    $template = $stmt->fetch();
    
    if ($template) {
        $template_path = ROOT_PATH . '/' . $template['image_path'];
        $output_path = ROOT_PATH . '/uploads/generated/test_' . time() . '.jpg';
        $test_message = "Test Message";
        
        $result = generateFestivalImage($template_path, null, $test_message, $output_path);
        
        if ($result['success']) {
            echo "✅ Image generation successful<br>";
            echo "Generated file: " . basename($output_path) . "<br>";
            if (file_exists($output_path)) {
                $size = getimagesize($output_path);
                echo "File size: " . filesize($output_path) . " bytes<br>";
                echo "Dimensions: {$size[0]}x{$size[1]}<br>";
                
                // Clean up test file
                unlink($output_path);
                echo "Test file cleaned up<br>";
            }
        } else {
            echo "❌ Image generation failed: " . $result['message'] . "<br>";
        }
    } else {
        echo "❌ No templates available for testing<br>";
    }
} catch (Exception $e) {
    echo "❌ Image generation test failed: " . $e->getMessage() . "<br>";
}

// Test 6: Check PHP configuration
echo "<h2>6. PHP Configuration Test</h2>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "Post Max Size: " . ini_get('post_max_size') . "<br>";
echo "Max Execution Time: " . ini_get('max_execution_time') . "<br>";

echo "<h2>Test Complete</h2>";
echo "<p><a href='index.php'>← Back to Homepage</a></p>";
?>
