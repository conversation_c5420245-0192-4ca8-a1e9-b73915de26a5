<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

// Require user login
requireLogin();

$user_id = getCurrentUserId();

// Debug information
if (!$user_id) {
    setFlashMessage('error', 'No user ID in session. Please log in again.');
    redirect('quick_login.php');
}

$user = getUserById($pdo, $user_id);
if (!$user) {
    setFlashMessage('error', 'User not found in database (ID: ' . $user_id . '). Please log in again.');
    redirect('quick_login.php');
}

// Get all festivals for selection
$festivals = getAllFestivals($pdo);

// Get templates for selected festival
$selected_festival_id = $_GET['festival'] ?? ($festivals[0]['id'] ?? null);
$templates = [];
if ($selected_festival_id) {
    $templates = getTemplatesByFestival($pdo, $selected_festival_id);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Image Generator - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Poppins', sans-serif !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            min-height: 100vh;
        }

        .generator-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            margin: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .modern-navbar {
            background: rgba(255, 255, 255, 0.15) !important;
            backdrop-filter: blur(20px);
            border: none !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
            padding: 15px 0 !important;
        }

        .navbar-brand {
            font-weight: 800 !important;
            font-size: 1.8rem !important;
            color: white !important;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500 !important;
            padding: 10px 20px !important;
            border-radius: 25px !important;
            transition: all 0.3s ease !important;
        }

        .nav-link:hover, .nav-link.active {
            background: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            transform: translateY(-2px);
        }

        .generator-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            margin-bottom: 30px;
        }

        .generator-header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
        }

        .modern-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            border: none;
            transition: all 0.3s ease;
        }

        .modern-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .form-control, .form-select {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-weight: 700;
            color: white;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }

        .btn-modern:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .template-card {
            border: 3px solid transparent;
            border-radius: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
            overflow: hidden;
        }

        .template-card:hover, .template-card.selected {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .template-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 10px;
        }

        .alert {
            border-radius: 15px;
            border: none;
            padding: 20px;
            margin-bottom: 30px;
        }

        .alert-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
        }

        .alert-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
            color: white;
        }

        .preview-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .preview-container img {
            max-width: 100%;
            max-height: 400px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .file-upload-area {
            border: 3px dashed #dee2e6;
            border-radius: 15px;
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .file-upload-area:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .file-upload-area.dragover {
            border-color: #667eea;
            background: #f0f8ff;
            transform: scale(1.02);
        }

        .template-selector {
            max-height: 400px;
            overflow-y: auto;
            padding: 10px;
            border-radius: 15px;
            background: #f8f9fa;
        }

        .template-selector::-webkit-scrollbar {
            width: 8px;
        }

        .template-selector::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .template-selector::-webkit-scrollbar-thumb {
            background: #667eea;
            border-radius: 10px;
        }

        .template-selector::-webkit-scrollbar-thumb:hover {
            background: #5a6fd8;
        }

        /* Fix for right sidebar layout */
        @media (min-width: 992px) {
            .col-lg-4 {
                position: sticky;
                top: 120px;
                height: fit-content;
            }
        }

        .uploaded-file-info {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }

        .uploaded-file-info .file-name {
            font-weight: 600;
            color: #155724;
        }

        .uploaded-file-info .file-size {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- Modern Navigation -->
    <nav class="navbar navbar-expand-lg modern-navbar">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-sparkles me-2"></i><?php echo APP_NAME; ?>
            </a>
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <i class="fas fa-bars text-white"></i>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="free_generator.php">
                            <i class="fas fa-magic me-2"></i>Free Generator
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="kit_generator.php">
                            <i class="fas fa-box me-2"></i>Kit Generator
                        </a>
                    </li>
                    <?php if (($user['package_type'] ?? '') === 'premium'): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="social_posting.php">
                            <i class="fas fa-share-alt me-2"></i>Auto Posting
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="template_designer.php">
                            <i class="fas fa-paint-brush me-2"></i>Template Designer
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-2"></i><?php echo htmlspecialchars($user['name'] ?? 'User'); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="generator-container">
        <?php displayFlashMessage(); ?>

        <div class="generator-header">
            <h1><i class="fas fa-magic me-3"></i>Free Festival Image Generator</h1>
            <p class="mb-0">Create a beautiful festival image with your logo and custom message</p>
        </div>

        <div class="row">
            <!-- Generator Form -->
            <div class="col-lg-8">
                <div class="modern-card">
                    <h3 class="mb-4"><i class="fas fa-cogs me-2"></i>Generator Settings</h3>
                        <form id="image-generator-form" enctype="multipart/form-data">
                            <?php echo getCSRFTokenField(); ?>
                            
                            <!-- Festival Selection -->
                            <div class="mb-4">
                                <label class="form-label">
                                    <i class="fas fa-calendar me-2"></i>Select Festival
                                </label>
                                <select class="form-select" name="festival_id" id="festival_id" required>
                                    <option value="">Choose a festival...</option>
                                    <?php foreach ($festivals as $festival): ?>
                                        <option value="<?php echo $festival['id']; ?>" 
                                                <?php echo $festival['id'] == $selected_festival_id ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($festival['name']); ?> 
                                            (<?php echo date('M j, Y', strtotime($festival['date'])); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Template Selection -->
                            <div class="mb-4" id="template-section" <?php echo empty($templates) ? 'style="display:none;"' : ''; ?>>
                                <label class="form-label">
                                    <i class="fas fa-images me-2"></i>Choose Template
                                </label>
                                <div class="template-selector">
                                    <div class="row" id="templates-container">
                                        <?php foreach ($templates as $template): ?>
                                            <div class="col-md-4 mb-3">
                                                <div class="template-card template-option" data-template-id="<?php echo $template['id']; ?>">
                                                    <img src="<?php echo getUploadUrl($template['image_path']); ?>"
                                                         alt="<?php echo htmlspecialchars($template['name']); ?>">
                                                    <div class="text-center mt-2">
                                                        <small class="fw-semibold"><?php echo htmlspecialchars($template['name']); ?></small>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <input type="hidden" name="template_id" id="selected_template" required>
                            </div>

                            <!-- Logo Upload -->
                            <div class="mb-4">
                                <label class="form-label">
                                    <i class="fas fa-upload me-2"></i>Upload Your Logo (Optional)
                                </label>
                                <div class="file-upload-area" id="file-upload-area">
                                    <input type="file" id="logo" name="logo" accept="image/*" style="display: none;">
                                    <div class="text-center">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <p class="mb-2"><strong>Click to upload</strong> or drag and drop</p>
                                        <small class="text-muted">PNG, JPG up to 5MB</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Custom Message -->
                            <div class="mb-4">
                                <label for="message" class="form-label">
                                    <i class="fas fa-comment me-2"></i>Custom Message
                                </label>
                                <textarea class="form-control" id="message" name="message" rows="3" 
                                          maxlength="<?php echo MAX_MESSAGE_LENGTH; ?>" 
                                          placeholder="Enter your festival message (optional)"></textarea>
                                <div class="form-text">
                                    <span id="char-count">0</span>/<?php echo MAX_MESSAGE_LENGTH; ?> characters
                                </div>
                            </div>

                            <!-- Generate Button -->
                            <div class="d-grid">
                                <button type="submit" class="btn btn-modern btn-lg">
                                    <i class="fas fa-magic me-2"></i>Generate Image
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            

            <!-- Preview & Download -->
            <div class="col-lg-4">
                <div class="modern-card">
                    <h3 class="mb-4"><i class="fas fa-eye me-2"></i>Preview & Download</h3>

                    <div id="preview-section" style="display: none;">
                        <div class="preview-container mb-3">
                            <img id="preview-image" src="" alt="Generated Image">
                        </div>
                    </div>

                    <div id="download-section" style="display: none;" class="text-center">
                        <!-- Download button will be inserted here -->
                    </div>

                    <div id="placeholder" class="preview-container">
                        <div>
                            <i class="fas fa-image fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">Your generated image will appear here</h5>
                            <p class="text-muted">Fill out the form and click "Generate Image" to create your festival image</p>
                        </div>
                    </div>
                </div>

                <!-- Tips Card -->
                <div class="modern-card">
                    <h5 class="mb-3">
                        <i class="fas fa-lightbulb me-2" style="color: #feca57;"></i>Tips for Best Results
                    </h5>
                    <ul class="list-unstyled mb-0">
                        <li class="mb-3 d-flex align-items-start">
                            <i class="fas fa-check-circle text-success me-3 mt-1"></i>
                            <span>Use high-quality PNG logos with transparent backgrounds for best overlay results</span>
                        </li>
                        <li class="mb-3 d-flex align-items-start">
                            <i class="fas fa-check-circle text-success me-3 mt-1"></i>
                            <span>Keep your message short and impactful - 2-3 words work best</span>
                        </li>
                        <li class="mb-3 d-flex align-items-start">
                            <i class="fas fa-check-circle text-success me-3 mt-1"></i>
                            <span>Square logos work best for positioning and scaling</span>
                        </li>
                        <li class="mb-0 d-flex align-items-start">
                            <i class="fas fa-check-circle text-success me-3 mt-1"></i>
                            <span>Generated images are high-quality 1200x800px perfect for social media</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Character counter for message
            $('#message').on('input', function() {
                var length = $(this).val().length;
                $('#char-count').text(length);
                
                if (length > <?php echo MAX_MESSAGE_LENGTH; ?>) {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid');
                }
            });

            // Festival selection change
            $('#festival_id').change(function() {
                var festivalId = $(this).val();
                if (festivalId) {
                    // Load templates for selected festival
                    $.get('get_templates.php', {festival_id: festivalId}, function(data) {
                        if (data.success) {
                            var templatesHtml = '';
                            data.templates.forEach(function(template) {
                                templatesHtml += '<div class="col-md-4 mb-3">' +
                                    '<div class="template-card template-option" data-template-id="' + template.id + '">' +
                                    '<img src="' + template.image_url + '" alt="' + template.name + '">' +
                                    '<div class="text-center mt-2">' +
                                    '<small class="fw-semibold">' + template.name + '</small>' +
                                    '</div></div></div>';
                            });
                            $('#templates-container').html(templatesHtml);
                            $('#template-section').show();
                            $('#selected_template').val('');
                        }
                    }, 'json');
                } else {
                    $('#template-section').hide();
                }
            });

            // Template selection
            $(document).on('click', '.template-option', function() {
                $('.template-card').removeClass('selected');
                $(this).addClass('selected');
                $('#selected_template').val($(this).data('template-id'));
            });

            // File upload drag and drop functionality
            const $fileUploadArea = $('#file-upload-area');
            const $fileInput = $('#logo');

            // Click to upload
            $fileUploadArea.on('click', function() {
                $fileInput.click();
            });

            // Drag and drop events
            $fileUploadArea.on('dragover dragenter', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('dragover');
            });

            $fileUploadArea.on('dragleave dragend', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('dragover');
            });

            $fileUploadArea.on('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('dragover');

                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    if (file.type.startsWith('image/')) {
                        $fileInput[0].files = files;
                        showUploadedFile(file);
                    } else {
                        showAlert('Please upload an image file (PNG, JPG, JPEG)', 'error');
                    }
                }
            });

            // File input change
            $fileInput.on('change', function() {
                const file = this.files[0];
                if (file) {
                    showUploadedFile(file);
                }
            });

            // Show uploaded file info
            function showUploadedFile(file) {
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                const fileInfo = `
                    <div class="uploaded-file-info">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-image text-success me-2"></i>
                            <div class="flex-grow-1">
                                <div class="file-name">${file.name}</div>
                                <div class="file-size">${fileSize} MB</div>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearUploadedFile()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `;

                $('#file-upload-area').after(fileInfo);
                $('#file-upload-area').hide();
            }

            // Clear uploaded file
            window.clearUploadedFile = function() {
                $fileInput.val('');
                $('.uploaded-file-info').remove();
                $('#file-upload-area').show();
            };

            // Form submission with better error handling
            $('#image-generator-form').submit(function(e) {
                e.preventDefault();

                const $form = $(this);
                const $submitBtn = $form.find('button[type="submit"]');
                const originalText = $submitBtn.html();

                // Show loading state
                $submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Generating...');

                // Hide previous results
                $('#preview-section, #download-section').hide();
                $('#placeholder').show();

                // Create FormData for file upload
                const formData = new FormData(this);

                $.ajax({
                    url: 'generate_image.php',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            // Show success message
                            showAlert('Image generated successfully!', 'success');

                            // Display preview
                            $('#preview-image').attr('src', response.image_url);
                            $('#placeholder').hide();
                            $('#preview-section').show();

                            // Show download button
                            const downloadBtn = `
                                <a href="${response.download_url}" class="btn btn-modern btn-lg mb-3" download>
                                    <i class="fas fa-download me-2"></i>Download Image
                                </a>
                                <br>
                                <small class="text-muted">High-quality 1200x800px image</small>
                            `;
                            $('#download-section').html(downloadBtn).show();

                        } else {
                            showAlert(response.message || 'Failed to generate image. Please try again.', 'error');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', error);
                        console.error('Response Text:', xhr.responseText);
                        console.error('Status:', status);

                        // Try to show more specific error
                        let errorMessage = 'An error occurred while generating the image. Please try again.';
                        if (xhr.responseText) {
                            // If response contains HTML, extract error message
                            if (xhr.responseText.includes('<div class="alert alert-danger">')) {
                                const match = xhr.responseText.match(/<div class="alert alert-danger"[^>]*>(.*?)<\/div>/);
                                if (match) {
                                    errorMessage = match[1].replace(/<[^>]*>/g, '').trim();
                                }
                            } else if (xhr.responseText.length < 200) {
                                errorMessage = xhr.responseText;
                            }
                        }

                        showAlert(errorMessage, 'error');
                    },
                    complete: function() {
                        // Reset button
                        $submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            });

            // Alert function for better user feedback
            function showAlert(message, type) {
                const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
                const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';

                const alertHtml = `
                    <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                        <i class="${icon} me-2"></i>${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;

                // Remove existing alerts
                $('.alert').remove();

                // Add new alert at the top of the container
                $('.generator-container').prepend(alertHtml);

                // Auto-hide success alerts after 5 seconds
                if (type === 'success') {
                    setTimeout(function() {
                        $('.alert-success').fadeOut();
                    }, 5000);
                }

                // Scroll to top to show alert
                $('html, body').animate({
                    scrollTop: $('.generator-container').offset().top - 100
                }, 500);
            }
        });
    </script>
</body>
</html>
