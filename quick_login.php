<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

// If already logged in, redirect to dashboard
if (isLoggedIn()) {
    redirect('dashboard.php');
}

$message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if ($email && $password) {
        try {
            $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($password, $user['password'])) {
                loginUser($user['id'], $user['name'], $user['email']);
                $message = "<p style='color: green;'>✅ Login successful! Redirecting...</p>";
                echo "<script>setTimeout(function(){ window.location.href = 'dashboard.php'; }, 1500);</script>";
            } else {
                $message = "<p style='color: red;'>❌ Invalid email or password</p>";
            }
        } catch (Exception $e) {
            $message = "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
        }
    } else {
        $message = "<p style='color: red;'>❌ Please enter both email and password</p>";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Login - FestivalKit</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
    </style>
</head>
<body>
    <div class="login-card">
        <h2 class="text-center mb-4">🎉 Quick Login</h2>
        
        <?php echo $message; ?>
        
        <form method="post">
            <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" name="email" required 
                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
            </div>
            
            <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>
            
            <button type="submit" class="btn btn-primary w-100 mb-3">Login</button>
        </form>
        
        <hr>
        
        <div class="text-center">
            <h6>Test Credentials:</h6>
            <p class="mb-1"><strong>Email:</strong> <EMAIL></p>
            <p class="mb-3"><strong>Password:</strong> test123</p>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="email" value="<EMAIL>">
                <input type="hidden" name="password" value="test123">
                <button type="submit" class="btn btn-success btn-sm">Quick Login as Test User</button>
            </form>
        </div>
        
        <hr>
        
        <div class="text-center">
            <small>
                <a href="check_database.php">Check Database</a> | 
                <a href="setup_test_user.php">Setup Test User</a> | 
                <a href="debug_session.php">Debug Session</a>
            </small>
        </div>
    </div>
</body>
</html>
