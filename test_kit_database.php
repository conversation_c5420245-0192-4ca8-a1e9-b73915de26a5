<?php
session_start();
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

// Create a test session if not logged in
if (!isLoggedIn()) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'Test User';
    $_SESSION['user_email'] = '<EMAIL>';
}

$user_id = getCurrentUserId();

echo "<h1>🗄️ Kit Generator Database Test</h1>";

// Test 1: Check current user uploads
echo "<h2>1. Current User Uploads</h2>";
try {
    $stmt = $pdo->prepare("SELECT * FROM user_uploads WHERE user_id = ? ORDER BY created_at DESC");
    $stmt->execute([$user_id]);
    $uploads = $stmt->fetchAll();
    
    if (empty($uploads)) {
        echo "❌ No uploads found for user ID: $user_id<br>";
    } else {
        echo "✅ Found " . count($uploads) . " upload(s) for user ID: $user_id<br>";
        foreach ($uploads as $upload) {
            echo "<div style='background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            echo "<strong>Upload ID:</strong> {$upload['id']}<br>";
            echo "<strong>Logo Path:</strong> {$upload['logo_path']}<br>";
            echo "<strong>Tagline:</strong> {$upload['tagline']}<br>";
            echo "<strong>Contact Info:</strong> {$upload['contact_info']}<br>";
            echo "<strong>Created:</strong> {$upload['created_at']}<br>";
            
            // Check if file exists
            $full_path = ROOT_PATH . '/' . $upload['logo_path'];
            if (file_exists($full_path)) {
                echo "<strong>File Status:</strong> ✅ Exists<br>";
                $preview_url = getUploadUrl2($upload['logo_path']);
                echo "<strong>Preview:</strong> <img src='$preview_url' style='max-width: 100px; max-height: 100px; border: 1px solid #ccc;'><br>";
            } else {
                echo "<strong>File Status:</strong> ❌ Missing ($full_path)<br>";
            }
            echo "</div>";
        }
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test 2: Test file upload simulation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_logo'])) {
    echo "<h2>2. Upload Test Results</h2>";
    
    $tagline = sanitize_input($_POST['tagline'] ?? 'Test Tagline');
    $contact_info = sanitize_input($_POST['contact_info'] ?? '<EMAIL>');
    
    try {
        if ($_FILES['test_logo']['error'] === UPLOAD_ERR_OK) {
            // Upload file
            $upload_result = uploadFile($_FILES['test_logo'], ROOT_PATH . '/' . UPLOAD_DIR_LOGOS);
            
            if ($upload_result['success']) {
                echo "✅ File uploaded successfully<br>";
                $logo_path = $upload_result['filepath'];
                $logo_path_for_db = str_replace(ROOT_PATH . '/', '', $logo_path);
                
                echo "Full path: $logo_path<br>";
                echo "DB path: $logo_path_for_db<br>";
                
                // Save to database
                $stmt = $pdo->prepare("INSERT INTO user_uploads (user_id, logo_path, tagline, contact_info) VALUES (?, ?, ?, ?) 
                                      ON DUPLICATE KEY UPDATE logo_path = VALUES(logo_path), tagline = VALUES(tagline), contact_info = VALUES(contact_info)");
                $stmt->execute([$user_id, $logo_path_for_db, $tagline, $contact_info]);
                
                echo "✅ Database record saved<br>";
                echo "<div style='color: green; font-weight: bold;'>SUCCESS: Upload and database save completed!</div>";
                
                // Refresh to show updated data
                echo "<script>setTimeout(function(){ location.reload(); }, 2000);</script>";
                
            } else {
                echo "❌ Upload failed: " . $upload_result['message'] . "<br>";
            }
        } else {
            echo "❌ File upload error: " . $_FILES['test_logo']['error'] . "<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "<br>";
    }
}

// Test 3: Check upload directory
echo "<h2>3. Upload Directory Check</h2>";
$upload_dir = ROOT_PATH . '/' . UPLOAD_DIR_LOGOS;
if (is_dir($upload_dir)) {
    if (is_writable($upload_dir)) {
        echo "✅ Upload directory is writable: $upload_dir<br>";
        
        // List files in directory
        $files = glob($upload_dir . '/*');
        echo "Files in directory: " . count($files) . "<br>";
        foreach (array_slice($files, 0, 5) as $file) {
            echo "- " . basename($file) . "<br>";
        }
    } else {
        echo "❌ Upload directory is not writable: $upload_dir<br>";
    }
} else {
    echo "❌ Upload directory does not exist: $upload_dir<br>";
}
?>

<h2>4. Test Upload Form</h2>
<form method="POST" enctype="multipart/form-data" style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
    <div style="margin-bottom: 15px;">
        <label><strong>Test Logo:</strong></label><br>
        <input type="file" name="test_logo" accept="image/*" required>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label><strong>Tagline:</strong></label><br>
        <input type="text" name="tagline" value="Test Tagline <?php echo date('H:i:s'); ?>" style="width: 300px; padding: 8px;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label><strong>Contact Info:</strong></label><br>
        <input type="text" name="contact_info" value="<EMAIL> | 123-456-7890" style="width: 300px; padding: 8px;">
    </div>
    
    <button type="submit" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px;">
        Test Upload & Database Save
    </button>
</form>

<h2>5. Clear Test Data</h2>
<?php if (isset($_GET['clear']) && $_GET['clear'] === 'yes'): ?>
    <?php
    try {
        // Delete database records
        $stmt = $pdo->prepare("DELETE FROM user_uploads WHERE user_id = ?");
        $stmt->execute([$user_id]);
        
        // Delete uploaded files
        $files = glob(ROOT_PATH . '/' . UPLOAD_DIR_LOGOS . '/logo_' . $user_id . '_*');
        foreach ($files as $file) {
            unlink($file);
        }
        
        echo "<div style='color: green;'>✅ Test data cleared successfully!</div>";
        echo "<script>setTimeout(function(){ location.href = 'test_kit_database.php'; }, 1000);</script>";
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ Error clearing data: " . $e->getMessage() . "</div>";
    }
    ?>
<?php else: ?>
    <a href="?clear=yes" onclick="return confirm('Clear all test data?')" 
       style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        Clear Test Data
    </a>
<?php endif; ?>

<h2>🔗 Quick Links</h2>
<div style="margin: 20px 0;">
    <a href="kit_generator.php" style="display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">Kit Generator</a>
    <a href="test_kit_upload.php" style="display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">Upload Test</a>
    <a href="index.php" style="display: inline-block; padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px; margin: 5px;">Homepage</a>
</div>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2 { color: #333; }
</style>
