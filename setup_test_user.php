<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'config/session.php';
require_once 'includes/functions.php';

echo "<h1>Setup Test User</h1>";

try {
    // Check if test user exists
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $existing_user = $stmt->fetch();
    
    if ($existing_user) {
        echo "<p style='color: green;'>✅ Test user already exists!</p>";
        echo "<p><strong>ID:</strong> " . $existing_user['id'] . "</p>";
        echo "<p><strong>Name:</strong> " . htmlspecialchars($existing_user['name']) . "</p>";
        echo "<p><strong>Email:</strong> " . htmlspecialchars($existing_user['email']) . "</p>";
        echo "<p><strong>Package:</strong> " . htmlspecialchars($existing_user['package_type']) . "</p>";
    } else {
        // Create test user
        $password_hash = password_hash('test123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (name, email, password, package_type, created_at) VALUES (?, ?, ?, ?, NOW())");
        $stmt->execute(['Test User', '<EMAIL>', $password_hash, 'premium']);
        
        $user_id = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ Test user created successfully!</p>";
        echo "<p><strong>ID:</strong> " . $user_id . "</p>";
        echo "<p><strong>Email:</strong> <EMAIL></p>";
        echo "<p><strong>Password:</strong> test123</p>";
        echo "<p><strong>Package:</strong> premium</p>";
    }
    
    // Auto-login the test user
    if (!isLoggedIn()) {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $user = $stmt->fetch();
        
        if ($user) {
            loginUser($user['id'], $user['name'], $user['email']);
            echo "<p style='color: blue;'>🔐 Automatically logged in as test user!</p>";
        }
    } else {
        echo "<p style='color: blue;'>🔐 Already logged in as: " . htmlspecialchars($_SESSION['user_name'] ?? 'Unknown') . "</p>";
    }
    
    echo "<hr>";
    echo "<h3>Test Links:</h3>";
    echo "<p><a href='dashboard.php' style='color: blue;'>📊 Go to Dashboard</a></p>";
    echo "<p><a href='free_generator.php' style='color: blue;'>🎨 Go to Free Generator</a></p>";
    echo "<p><a href='kit_generator.php' style='color: blue;'>📦 Go to Kit Generator</a></p>";
    echo "<p><a href='template_designer.php' style='color: blue;'>🎨 Go to Template Designer</a></p>";
    
    echo "<hr>";
    echo "<h3>Session Info:</h3>";
    echo "<pre>";
    print_r($_SESSION);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
